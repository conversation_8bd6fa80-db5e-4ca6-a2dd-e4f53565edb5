# STM32F4 嵌入式开发规范与接口使用指南

## 概述

本文档为STM32F4智能小车工程提供完整的开发规范、接口使用指南和最佳实践。通过统一的编码标准、模块化设计原则和规范化开发流程，帮助开发者建立良好的编程习惯，提高代码质量和项目可维护性。

### 文档适用对象
- 嵌入式开发初学者
- STM32F4项目开发人员
- 代码审查人员
- 项目维护人员

## 工程架构设计规范

### 1. 三层架构设计原则

```
┌─────────────────────────────────────────────────────────┐
│                   Scheduler Layer                       │
│                   (调度层)                              │
│  ┌─────────────────────────────────────────────────────┐ │
│  │              Task Management                        │ │
│  │              Time Scheduling                        │ │
│  │              System Coordination                    │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
                            ↕
┌─────────────────────────────────────────────────────────┐
│                   Application Layer                     │
│                   (应用层)                              │
│  ┌─────────────────────────────────────────────────────┐ │
│  │              Business Logic                         │ │
│  │              Algorithm Implementation               │ │
│  │              Data Processing                        │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
                            ↕
┌─────────────────────────────────────────────────────────┐
│                   Driver Layer                          │
│                   (驱动层)                              │
│  ┌─────────────────────────────────────────────────────┐ │
│  │              Hardware Abstraction                   │ │
│  │              Register Operations                     │ │
│  │              Peripheral Control                     │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

#### 层次职责定义

**Driver Layer (驱动层)**
- **职责**: 硬件抽象、寄存器操作、外设控制
- **原则**: 只关注硬件操作，不包含业务逻辑
- **接口**: 提供标准化的硬件操作接口

**Application Layer (应用层)**
- **职责**: 业务逻辑实现、算法处理、数据管理
- **原则**: 调用驱动层接口，实现具体功能
- **接口**: 向调度层提供任务接口

**Scheduler Layer (调度层)**
- **职责**: 任务管理、时间调度、系统协调
- **原则**: 统一管理所有任务的执行
- **接口**: 系统级任务调度接口

### 2. 目录结构规范

```
STM32F4_Project/
├── Core/                    # STM32CubeMX生成的核心文件
│   ├── Inc/                 # 核心头文件
│   └── Src/                 # 核心源文件
├── Drivers/                 # STM32 HAL驱动库
├── User/                    # 用户代码目录
│   ├── Driver/              # 驱动层代码
│   │   ├── led_driver.h/.c      # LED驱动
│   │   ├── motor_driver.h/.c    # 电机驱动
│   │   ├── encoder_driver.h/.c  # 编码器驱动
│   │   ├── uart_driver.h/.c     # 串口驱动
│   │   ├── oled_driver.h/.c     # OLED驱动
│   │   └── key_driver.h/.c      # 按键驱动
│   ├── App/                 # 应用层代码
│   │   ├── led_app.h/.c         # LED应用
│   │   ├── motor_app.h/.c       # 电机应用
│   │   ├── encoder_app.h/.c     # 编码器应用
│   │   ├── uart_app.h/.c        # 串口应用
│   │   ├── oled_app.h/.c        # OLED应用
│   │   ├── mpu6050_app.h/.c     # MPU6050应用
│   │   ├── gray_app.h/.c        # 灰度传感器应用
│   │   ├── key_app.h/.c         # 按键应用
│   │   └── pid_app.h/.c         # PID控制应用
│   ├── Module/              # 第三方模块
│   │   ├── PID/                 # PID算法模块
│   │   ├── MPU6050/             # MPU6050传感器模块
│   │   ├── 0.96 Oled/           # OLED显示模块
│   │   ├── Ebtn/                # 高级按键处理模块
│   │   ├── Ringbuffer/          # 环形缓冲区模块
│   │   ├── Grayscale/           # 灰度传感器模块
│   │   └── WouoUI-Page/         # UI界面框架
│   ├── Scheduler/           # 调度层代码
│   │   ├── Scheduler.h/.c       # 核心调度器
│   │   └── Scheduler_Task.h/.c  # 任务管理
│   └── MyDefine.h           # 统一头文件管理
├── docs/                    # 项目文档
│   ├── architecture/        # 架构文档
│   ├── drivers/             # 驱动文档
│   ├── applications/        # 应用文档
│   ├── modules/             # 模块文档
│   ├── pid-control/         # PID控制专题
│   └── development/         # 开发指南
└── README.md                # 项目说明
```

## 编码规范标准

### 1. 命名规范

#### 文件命名规范
```c
// 头文件命名：模块名_层次.h
led_driver.h        // LED驱动层头文件
motor_app.h         // 电机应用层头文件
pid_app.h           // PID应用层头文件

// 源文件命名：模块名_层次.c
led_driver.c        // LED驱动层源文件
motor_app.c         // 电机应用层源文件
pid_app.c           // PID应用层源文件
```

#### 变量命名规范
```c
// 全局变量：g_模块名_变量名
float g_line_position_error;    // 全局循迹误差变量
bool g_system_running;          // 全局系统运行状态

// 局部变量：小写字母+下划线
int motor_speed;                // 电机速度
float pid_output;               // PID输出
uint8_t sensor_data[8];         // 传感器数据

// 常量：大写字母+下划线
#define MAX_SPEED 1000          // 最大速度
#define MIN_SPEED -1000         // 最小速度
#define PID_CONTROL_PERIOD 20   // PID控制周期
```

#### 函数命名规范
```c
// 驱动层函数：模块名_操作名
void Led_Display(uint8_t enable);           // LED显示控制
void Motor_Set_Speed(MOTOR* motor, int speed); // 电机速度设置
void Encoder_Reset_Count(Encoder* encoder);  // 编码器计数复位

// 应用层函数：模块名_功能名
void LED_Task(void);                        // LED任务
void Motor_Control_Task(void);              // 电机控制任务
void PID_Task(void);                        // PID控制任务

// 初始化函数：模块名_Init
void Motor_Init(void);                      // 电机初始化
void PID_Init(void);                        // PID初始化
void Scheduler_Init(void);                  // 调度器初始化
```

#### 结构体命名规范
```c
// 结构体类型：大写字母+下划线，以_T结尾
typedef struct {
    float kp, ki, kd;           // PID参数
    float target, current, out; // 控制变量
    float error, last_error;    // 误差变量
} PID_T;

// 配置结构体：模块名_Config
typedef struct {
    TIM_HandleTypeDef *htim;    // 定时器句柄
    uint32_t pwm_channel;       // PWM通道
    GPIO_TypeDef *port;         // GPIO端口
    uint16_t pin;               // GPIO引脚
} MOTOR_Config;

// 实例结构体：模块名(大写)
typedef struct {
    MOTOR_Config config;        // 配置信息
    int speed;                  // 当前速度
    int dead_band_speed;        // 死区速度
} MOTOR;
```

### 2. 代码格式规范

#### 头文件保护
```c
#ifndef __MODULE_NAME_H__
#define __MODULE_NAME_H__

// 头文件内容

#endif
```

#### 包含文件顺序
```c
// 1. 统一头文件
#include "MyDefine.h"

// 2. 系统头文件
#include <stdio.h>
#include <string.h>

// 3. HAL库头文件
#include "main.h"
#include "gpio.h"

// 4. 用户头文件
#include "led_driver.h"
#include "motor_driver.h"
```

#### 函数格式规范
```c
/**
 * @brief 电机速度设置函数
 * @param motor 电机结构体指针
 * @param speed 目标速度 (-1000 ~ 1000)
 * @retval None
 */
void Motor_Set_Speed(MOTOR* motor, int speed)
{
    // 参数检查
    if(motor == NULL) return;
    
    // 速度限幅
    if(speed > 1000) speed = 1000;
    if(speed < -1000) speed = -1000;
    
    // 死区处理
    if(abs(speed) < motor->dead_band_speed) {
        speed = 0;
    }
    
    // 更新速度
    motor->speed = speed;
    
    // 硬件控制
    if(speed == 0) {
        Motor_Stop(motor);          // 停止电机
    } else if(speed > 0) {
        Motor_Forward(motor, speed); // 正转
    } else {
        Motor_Backward(motor, -speed); // 反转
    }
}
```

#### 注释规范
```c
/* ========== 模块功能说明 ========== */
// 单行注释：简短说明

/**
 * @brief 函数功能简述
 * @param param1 参数1说明
 * @param param2 参数2说明
 * @retval 返回值说明
 * @note 注意事项
 */

// 行内注释：变量或语句说明
int motor_speed = 0;    // 电机速度变量
```

### 3. 数据类型规范

#### 基础数据类型
```c
// 使用stdint.h标准类型
uint8_t  data_byte;     // 8位无符号整数
uint16_t data_word;     // 16位无符号整数
uint32_t data_dword;    // 32位无符号整数
int8_t   signed_byte;   // 8位有符号整数
int16_t  signed_word;   // 16位有符号整数
int32_t  signed_dword;  // 32位有符号整数

// 浮点数类型
float  float_value;     // 32位浮点数
double double_value;    // 64位浮点数

// 布尔类型
bool enable_flag;       // 布尔值 (true/false)
```

#### 枚举类型规范
```c
// 枚举命名：模块名_枚举名_E
typedef enum {
    MOTOR_STATE_STOP = 0,       // 停止状态
    MOTOR_STATE_FORWARD,        // 前进状态
    MOTOR_STATE_BACKWARD,       // 后退状态
    MOTOR_STATE_BRAKE           // 刹车状态
} Motor_State_E;

// 错误码枚举
typedef enum {
    ERROR_NONE = 0,             // 无错误
    ERROR_PARAM_INVALID,        // 参数无效
    ERROR_HARDWARE_FAULT,       // 硬件故障
    ERROR_TIMEOUT               // 超时错误
} Error_Code_E;
```

## 接口设计规范

### 1. 驱动层接口设计

#### 标准接口模板
```c
// 驱动层头文件模板 (xxx_driver.h)
#ifndef __XXX_DRIVER_H__
#define __XXX_DRIVER_H__

#include "MyDefine.h"

// 配置结构体
typedef struct {
    // 硬件配置参数
} XXX_Config;

// 实例结构体
typedef struct {
    XXX_Config config;
    // 状态变量
} XXX;

// 初始化函数
void XXX_Init(XXX* instance, XXX_Config* config);

// 基础操作函数
void XXX_Enable(XXX* instance);
void XXX_Disable(XXX* instance);
void XXX_Reset(XXX* instance);

// 数据操作函数
void XXX_Write_Data(XXX* instance, uint8_t* data, uint16_t length);
uint16_t XXX_Read_Data(XXX* instance, uint8_t* buffer, uint16_t max_length);

// 状态查询函数
bool XXX_Is_Ready(XXX* instance);
uint32_t XXX_Get_Status(XXX* instance);

#endif
```

#### 电机驱动接口示例
```c
// motor_driver.h
#ifndef __MOTOR_DRIVER_H__
#define __MOTOR_DRIVER_H__

#include "MyDefine.h"

// 电机配置结构体
typedef struct {
    TIM_HandleTypeDef *htim;    // PWM定时器
    uint32_t pwm_channel;       // PWM通道
    struct {
        GPIO_TypeDef *port;     // GPIO端口
        uint16_t pin;           // GPIO引脚
    } in1, in2;                 // 方向控制引脚
    uint8_t reverse;            // 反转标志
} MOTOR_Config;

// 电机实例结构体
typedef struct {
    MOTOR_Config config;        // 配置信息
    int speed;                  // 当前速度
    int dead_band_speed;        // 死区速度
} MOTOR;

// 接口函数声明
void Motor_Config_Init(MOTOR* motor, TIM_HandleTypeDef *htim, uint32_t pwm_channel, 
                       GPIO_TypeDef *in1_port, uint16_t in1_pin, 
                       GPIO_TypeDef *in2_port, uint16_t in2_pin, 
                       uint8_t reverse, int dead_band_speed);
void Motor_Set_Speed(MOTOR* motor, int speed);
void Motor_Stop(MOTOR* motor);
void Motor_Brake(MOTOR* motor);

#endif
```

### 2. 应用层接口设计

#### 标准接口模板
```c
// 应用层头文件模板 (xxx_app.h)
#ifndef __XXX_APP_H__
#define __XXX_APP_H__

#include "MyDefine.h"

// 应用参数结构体
typedef struct {
    // 应用层配置参数
} XXX_Params_t;

// 应用状态结构体
typedef struct {
    // 应用层状态变量
} XXX_Status_t;

// 初始化函数
void XXX_App_Init(void);

// 主任务函数
void XXX_Task(void);

// 配置函数
void XXX_Set_Params(XXX_Params_t* params);
void XXX_Get_Status(XXX_Status_t* status);

// 控制函数
void XXX_Start(void);
void XXX_Stop(void);
void XXX_Reset(void);

#endif
```

#### PID应用接口示例
```c
// pid_app.h
#ifndef __PID_APP_H__
#define __PID_APP_H__

#include "MyDefine.h"

// PID参数结构体
typedef struct {
    float kp;          // 比例系数
    float ki;          // 积分系数
    float kd;          // 微分系数
    float out_min;     // 输出最小值
    float out_max;     // 输出最大值
} PidParams_t;

// 接口函数声明
void PID_Init(void);                    // PID初始化
void PID_Task(void);                    // PID主任务
void PID_Set_Enable(bool enable);       // 使能控制
void PID_Set_Mode(uint8_t mode);        // 模式设置
void PID_Set_Target_Speed(float left_speed, float right_speed); // 目标速度设置

#endif
```

### 3. 调度层接口设计

#### 任务注册接口
```c
// Scheduler.h
#ifndef __SCHEDULER_H__
#define __SCHEDULER_H__

#include "MyDefine.h"

// 任务函数指针类型
typedef void (*Task_Function_t)(void);

// 任务结构体
typedef struct {
    Task_Function_t function;   // 任务函数
    uint32_t period;           // 执行周期(ms)
    uint32_t last_run;         // 上次执行时间
    bool enabled;              // 使能标志
} Task_t;

// 调度器接口
void Scheduler_Init(void);
void Scheduler_Add_Task(Task_Function_t function, uint32_t period);
void Scheduler_Remove_Task(Task_Function_t function);
void Scheduler_Enable_Task(Task_Function_t function, bool enable);
void Scheduler_Run(void);

#endif
```

## 模块扩展开发流程

### 1. 新模块添加标准流程

#### 步骤1：需求分析
```
1. 确定模块功能需求
2. 分析硬件接口要求
3. 定义模块在三层架构中的位置
4. 设计模块接口规范
```

#### 步骤2：创建文件结构
```bash
# 驱动层文件 (如果需要)
User/Driver/new_module_driver.h
User/Driver/new_module_driver.c

# 应用层文件
User/App/new_module_app.h
User/App/new_module_app.c

# 第三方模块文件 (如果需要)
User/Module/NewModule/new_module.h
User/Module/NewModule/new_module.c
```

#### 步骤3：实现驱动层
```c
// new_module_driver.h
#ifndef __NEW_MODULE_DRIVER_H__
#define __NEW_MODULE_DRIVER_H__

#include "MyDefine.h"

// 配置结构体
typedef struct {
    // 硬件配置参数
    GPIO_TypeDef *gpio_port;
    uint16_t gpio_pin;
    // 其他硬件参数
} NEW_MODULE_Config;

// 实例结构体
typedef struct {
    NEW_MODULE_Config config;
    // 状态变量
    uint8_t status;
    uint32_t data;
} NEW_MODULE;

// 接口函数
void New_Module_Init(NEW_MODULE* module, NEW_MODULE_Config* config);
void New_Module_Enable(NEW_MODULE* module);
void New_Module_Disable(NEW_MODULE* module);
uint32_t New_Module_Read_Data(NEW_MODULE* module);
void New_Module_Write_Data(NEW_MODULE* module, uint32_t data);

#endif
```

#### 步骤4：实现应用层
```c
// new_module_app.h
#ifndef __NEW_MODULE_APP_H__
#define __NEW_MODULE_APP_H__

#include "MyDefine.h"

// 应用参数
typedef struct {
    uint32_t update_period;     // 更新周期
    uint32_t threshold_value;   // 阈值
    bool auto_mode;            // 自动模式
} NewModule_Params_t;

// 接口函数
void New_Module_App_Init(void);
void New_Module_Task(void);
void New_Module_Set_Params(NewModule_Params_t* params);
void New_Module_Start(void);
void New_Module_Stop(void);

#endif
```

#### 步骤5：注册到调度器
```c
// 在Scheduler_Task.c中添加任务注册
void Scheduler_Task_Init(void)
{
    // 现有任务注册
    Scheduler_Add_Task(LED_Task, 100);
    Scheduler_Add_Task(Motor_Task, 20);
    Scheduler_Add_Task(PID_Task, 20);
    
    // 新模块任务注册
    Scheduler_Add_Task(New_Module_Task, 50);  // 50ms周期
}
```

#### 步骤6：更新头文件管理
```c
// 在MyDefine.h中添加新模块头文件
/* ========== 驱动层头文件 ========== */
#include "led_driver.h"
#include "motor_driver.h"
#include "new_module_driver.h"  // 新增

/* ========== 应用层头文件 ========== */
#include "led_app.h"
#include "motor_app.h"
#include "new_module_app.h"     // 新增
```

### 2. 模块集成测试流程

#### 单元测试
```c
// 新模块单元测试函数
void New_Module_Unit_Test(void)
{
    printf("=== New Module Unit Test ===\n");
    
    // 1. 初始化测试
    NEW_MODULE test_module;
    NEW_MODULE_Config config = {
        .gpio_port = GPIOA,
        .gpio_pin = GPIO_PIN_0
    };
    
    New_Module_Init(&test_module, &config);
    printf("Init Test: %s\n", test_module.status == 0 ? "PASS" : "FAIL");
    
    // 2. 功能测试
    New_Module_Enable(&test_module);
    uint32_t test_data = New_Module_Read_Data(&test_module);
    printf("Read Test: Data = %lu\n", test_data);
    
    // 3. 边界测试
    New_Module_Write_Data(&test_module, 0xFFFFFFFF);
    // 验证边界处理
    
    printf("=== Test Complete ===\n");
}
```

#### 集成测试
```c
// 模块集成测试
void System_Integration_Test(void)
{
    printf("=== System Integration Test ===\n");
    
    // 1. 初始化所有模块
    LED_App_Init();
    Motor_App_Init();
    New_Module_App_Init();
    
    // 2. 启动调度器
    Scheduler_Init();
    
    // 3. 运行测试序列
    for(int i = 0; i < 100; i++) {
        Scheduler_Run();
        HAL_Delay(10);
        
        // 检查系统状态
        if(i % 10 == 0) {
            printf("Test Step %d: System Running\n", i);
        }
    }
    
    printf("=== Integration Test Complete ===\n");
}

## 调试技巧与工具

### 1. 串口调试技术

#### 调试信息输出
```c
// 调试宏定义
#define DEBUG_ENABLE 1

#if DEBUG_ENABLE
    #define DEBUG_PRINTF(fmt, ...) printf("[DEBUG] " fmt "\r\n", ##__VA_ARGS__)
    #define ERROR_PRINTF(fmt, ...) printf("[ERROR] " fmt "\r\n", ##__VA_ARGS__)
    #define INFO_PRINTF(fmt, ...)  printf("[INFO]  " fmt "\r\n", ##__VA_ARGS__)
#else
    #define DEBUG_PRINTF(fmt, ...)
    #define ERROR_PRINTF(fmt, ...)
    #define INFO_PRINTF(fmt, ...)
#endif

// 使用示例
void Motor_Set_Speed(MOTOR* motor, int speed)
{
    DEBUG_PRINTF("Motor_Set_Speed: speed = %d", speed);

    if(motor == NULL) {
        ERROR_PRINTF("Motor_Set_Speed: motor pointer is NULL");
        return;
    }

    // 功能实现
    motor->speed = speed;
    INFO_PRINTF("Motor speed updated to %d", speed);
}
```

#### 系统状态监控
```c
// 系统状态监控函数
void System_Status_Monitor(void)
{
    static uint32_t last_print_time = 0;
    uint32_t current_time = HAL_GetTick();

    // 每1秒输出一次状态
    if(current_time - last_print_time >= 1000) {
        printf("\n=== System Status ===\n");
        printf("System Time: %lu ms\n", current_time);
        printf("Free Heap: %lu bytes\n", xPortGetFreeHeapSize());

        // 电机状态
        printf("Motor L: %d, Motor R: %d\n", left_motor.speed, right_motor.speed);

        // 编码器状态
        printf("Encoder L: %d, Encoder R: %d\n",
               left_encoder.count, right_encoder.count);

        // PID状态
        printf("PID Enable: %s, Mode: %s\n",
               pid_running ? "ON" : "OFF",
               pid_mode ? "Line" : "Angle");

        printf("=====================\n\n");
        last_print_time = current_time;
    }
}
```

#### 数据记录与分析
```c
// 数据记录结构体
typedef struct {
    uint32_t timestamp;
    float target_speed;
    float actual_speed;
    float pid_output;
    float error;
} Data_Record_t;

#define MAX_RECORDS 1000
Data_Record_t data_records[MAX_RECORDS];
uint16_t record_index = 0;

// 数据记录函数
void Record_Data(float target, float actual, float output, float error)
{
    if(record_index < MAX_RECORDS) {
        data_records[record_index].timestamp = HAL_GetTick();
        data_records[record_index].target_speed = target;
        data_records[record_index].actual_speed = actual;
        data_records[record_index].pid_output = output;
        data_records[record_index].error = error;
        record_index++;
    }
}

// 数据导出函数
void Export_Data_Records(void)
{
    printf("=== Data Export ===\n");
    printf("Time,Target,Actual,Output,Error\n");

    for(uint16_t i = 0; i < record_index; i++) {
        printf("%lu,%.2f,%.2f,%.2f,%.2f\n",
               data_records[i].timestamp,
               data_records[i].target_speed,
               data_records[i].actual_speed,
               data_records[i].pid_output,
               data_records[i].error);
    }

    printf("=== Export Complete ===\n");
}
```

### 2. LED指示调试

#### 状态指示系统
```c
// LED状态指示枚举
typedef enum {
    LED_STATUS_INIT = 0,        // 初始化状态
    LED_STATUS_RUNNING,         // 运行状态
    LED_STATUS_ERROR,           // 错误状态
    LED_STATUS_CALIBRATING,     // 校准状态
    LED_STATUS_IDLE             // 空闲状态
} LED_Status_E;

// LED指示模式
void LED_Status_Indicate(LED_Status_E status)
{
    static uint32_t last_toggle = 0;
    static bool led_state = false;
    uint32_t current_time = HAL_GetTick();

    switch(status) {
        case LED_STATUS_INIT:
            // 快速闪烁 (100ms)
            if(current_time - last_toggle >= 100) {
                led_state = !led_state;
                Led_Display(led_state);
                last_toggle = current_time;
            }
            break;

        case LED_STATUS_RUNNING:
            // 慢速闪烁 (500ms)
            if(current_time - last_toggle >= 500) {
                led_state = !led_state;
                Led_Display(led_state);
                last_toggle = current_time;
            }
            break;

        case LED_STATUS_ERROR:
            // 常亮
            Led_Display(1);
            break;

        case LED_STATUS_CALIBRATING:
            // 双闪模式
            if(current_time - last_toggle >= 200) {
                led_state = !led_state;
                Led_Display(led_state);
                last_toggle = current_time;
            }
            break;

        case LED_STATUS_IDLE:
            // 常灭
            Led_Display(0);
            break;
    }
}
```

### 3. 断言与错误处理

#### 断言宏定义
```c
// 断言宏
#define ASSERT(condition) \
    do { \
        if(!(condition)) { \
            printf("ASSERT FAILED: %s, line %d\n", __FILE__, __LINE__); \
            Error_Handler(); \
        } \
    } while(0)

// 参数检查宏
#define CHECK_PARAM(param) \
    do { \
        if((param) == NULL) { \
            ERROR_PRINTF("Parameter check failed: %s is NULL", #param); \
            return; \
        } \
    } while(0)

// 返回值检查宏
#define CHECK_RETURN(func_call, expected) \
    do { \
        if((func_call) != (expected)) { \
            ERROR_PRINTF("Function call failed: %s", #func_call); \
            return; \
        } \
    } while(0)
```

#### 错误处理框架
```c
// 错误代码定义
typedef enum {
    ERR_NONE = 0,
    ERR_PARAM_INVALID,
    ERR_HARDWARE_FAULT,
    ERR_TIMEOUT,
    ERR_MEMORY_FULL,
    ERR_COMMUNICATION_FAIL
} Error_Code_t;

// 错误处理函数
void Handle_Error(Error_Code_t error_code, const char* function_name)
{
    ERROR_PRINTF("Error in %s: Code %d", function_name, error_code);

    switch(error_code) {
        case ERR_PARAM_INVALID:
            // 参数错误处理
            break;

        case ERR_HARDWARE_FAULT:
            // 硬件故障处理
            LED_Status_Indicate(LED_STATUS_ERROR);
            break;

        case ERR_TIMEOUT:
            // 超时错误处理
            break;

        case ERR_MEMORY_FULL:
            // 内存不足处理
            break;

        case ERR_COMMUNICATION_FAIL:
            // 通信失败处理
            break;

        default:
            // 未知错误处理
            break;
    }
}
```

### 4. 性能分析工具

#### 执行时间测量
```c
// 时间测量宏
#define TIME_MEASURE_START() uint32_t start_time = HAL_GetTick()
#define TIME_MEASURE_END(name) \
    do { \
        uint32_t end_time = HAL_GetTick(); \
        DEBUG_PRINTF("%s execution time: %lu ms", name, end_time - start_time); \
    } while(0)

// 微秒级时间测量 (使用DWT)
void DWT_Init(void)
{
    CoreDebug->DEMCR |= CoreDebug_DEMCR_TRCENA_Msk;
    DWT->CTRL |= DWT_CTRL_CYCCNTENA_Msk;
    DWT->CYCCNT = 0;
}

#define TIME_MEASURE_US_START() uint32_t start_cycles = DWT->CYCCNT
#define TIME_MEASURE_US_END(name) \
    do { \
        uint32_t end_cycles = DWT->CYCCNT; \
        uint32_t cycles = end_cycles - start_cycles; \
        uint32_t us = cycles / (SystemCoreClock / 1000000); \
        DEBUG_PRINTF("%s execution time: %lu us", name, us); \
    } while(0)

// 使用示例
void PID_Task(void)
{
    TIME_MEASURE_US_START();

    // PID计算代码
    output_left = pid_calculate_incremental(&pid_speed_left, left_encoder.speed_cm_s);
    output_right = pid_calculate_incremental(&pid_speed_right, right_encoder.speed_cm_s);

    TIME_MEASURE_US_END("PID_Task");
}
```

#### 内存使用监控
```c
// 栈使用监控
void Stack_Usage_Monitor(void)
{
    extern uint32_t _estack;
    extern uint32_t _Min_Stack_Size;

    uint32_t stack_top = (uint32_t)&_estack;
    uint32_t current_sp;

    __asm volatile ("mov %0, sp" : "=r" (current_sp));

    uint32_t stack_used = stack_top - current_sp;
    uint32_t stack_free = current_sp - (stack_top - (uint32_t)&_Min_Stack_Size);

    DEBUG_PRINTF("Stack Usage: Used=%lu, Free=%lu", stack_used, stack_free);
}

// 堆使用监控
void Heap_Usage_Monitor(void)
{
    #ifdef configUSE_FREERTOS
    size_t free_heap = xPortGetFreeHeapSize();
    size_t min_free_heap = xPortGetMinimumEverFreeHeapSize();

    DEBUG_PRINTF("Heap Usage: Free=%u, MinFree=%u", free_heap, min_free_heap);
    #endif
}
```

## 最佳实践指南

### 1. 代码质量保证

#### 代码审查清单
```
□ 命名规范检查
  □ 变量名清晰明确
  □ 函数名动词开头
  □ 常量全大写
  □ 结构体类型以_T结尾

□ 代码格式检查
  □ 缩进统一(4个空格)
  □ 大括号位置统一
  □ 行长度不超过100字符
  □ 空行使用合理

□ 注释质量检查
  □ 函数有完整注释
  □ 复杂逻辑有说明
  □ 参数和返回值有说明
  □ 注释与代码同步

□ 错误处理检查
  □ 参数有效性检查
  □ 返回值检查
  □ 异常情况处理
  □ 资源释放正确

□ 性能考虑检查
  □ 避免不必要的计算
  □ 合理使用缓存
  □ 避免内存泄漏
  □ 中断处理简洁
```

#### 静态代码分析
```c
// 使用PC-lint或类似工具进行静态分析
// 常见问题检查：

// 1. 未初始化变量
int speed;  // 应该初始化
int speed = 0;  // 正确

// 2. 数组越界
uint8_t buffer[10];
buffer[10] = 0;  // 错误：越界
buffer[9] = 0;   // 正确

// 3. 空指针解引用
MOTOR* motor = NULL;
motor->speed = 100;  // 错误：空指针
if(motor != NULL) {  // 正确：检查后使用
    motor->speed = 100;
}

// 4. 内存泄漏
uint8_t* buffer = malloc(100);
// 使用buffer
// 忘记free(buffer);  // 错误：内存泄漏
free(buffer);  // 正确：释放内存
```

### 2. 性能优化技巧

#### 计算优化
```c
// 1. 避免浮点运算
// 慢速版本
float result = sin(angle) * 100.0f;

// 快速版本 (查表法)
const float sin_table[360] = { /* 预计算的正弦值 */ };
int angle_int = (int)(angle + 0.5f) % 360;
float result = sin_table[angle_int] * 100.0f;

// 2. 使用位运算
// 慢速版本
int result = value / 8;

// 快速版本
int result = value >> 3;  // 除以8等于右移3位

// 3. 避免重复计算
// 慢速版本
for(int i = 0; i < 100; i++) {
    result[i] = sqrt(a*a + b*b) * data[i];
}

// 快速版本
float magnitude = sqrt(a*a + b*b);  // 提取到循环外
for(int i = 0; i < 100; i++) {
    result[i] = magnitude * data[i];
}
```

#### 内存优化
```c
// 1. 使用合适的数据类型
// 浪费内存
int32_t small_counter;  // 只需要0-255

// 节省内存
uint8_t small_counter;  // 足够使用

// 2. 结构体成员对齐
// 低效布局 (12字节)
typedef struct {
    uint8_t flag;    // 1字节 + 3字节填充
    uint32_t value;  // 4字节
    uint8_t status;  // 1字节 + 3字节填充
} BadStruct;

// 高效布局 (8字节)
typedef struct {
    uint32_t value;  // 4字节
    uint8_t flag;    // 1字节
    uint8_t status;  // 1字节 + 2字节填充
} GoodStruct;

// 3. 使用栈变量而非堆变量
// 慢速版本
uint8_t* buffer = malloc(100);
// 使用buffer
free(buffer);

// 快速版本
uint8_t buffer[100];  // 栈上分配，自动释放
```

### 3. 可维护性设计

#### 模块化设计原则
```c
// 1. 单一职责原则
// 错误：一个函数做多件事
void Motor_And_LED_Control(int speed, bool led_on) {
    // 控制电机
    Motor_Set_Speed(&motor, speed);
    // 控制LED
    Led_Display(led_on);
}

// 正确：分离职责
void Motor_Control(int speed) {
    Motor_Set_Speed(&motor, speed);
}

void LED_Control(bool led_on) {
    Led_Display(led_on);
}

// 2. 接口隔离原则
// 错误：大而全的接口
typedef struct {
    void (*init)(void);
    void (*start)(void);
    void (*stop)(void);
    void (*calibrate)(void);
    void (*self_test)(void);
    void (*factory_reset)(void);
} Device_Interface;

// 正确：分离接口
typedef struct {
    void (*init)(void);
    void (*start)(void);
    void (*stop)(void);
} Basic_Interface;

typedef struct {
    void (*calibrate)(void);
    void (*self_test)(void);
} Advanced_Interface;
```

#### 配置管理
```c
// 配置参数集中管理
typedef struct {
    // 系统配置
    uint32_t system_clock;
    uint32_t tick_frequency;

    // 电机配置
    struct {
        int max_speed;
        int dead_band;
        uint8_t reverse_flag;
    } motor_config;

    // PID配置
    struct {
        float kp, ki, kd;
        float out_max, out_min;
    } pid_config;

    // 传感器配置
    struct {
        uint32_t update_period;
        uint16_t threshold;
    } sensor_config;
} System_Config_t;

// 配置加载和保存
void Config_Load_Default(System_Config_t* config);
void Config_Save_To_Flash(System_Config_t* config);
void Config_Load_From_Flash(System_Config_t* config);
```

### 4. 安全编程实践

#### 输入验证
```c
// 参数验证函数
bool Validate_Speed_Input(int speed) {
    if(speed < -1000 || speed > 1000) {
        ERROR_PRINTF("Invalid speed: %d (range: -1000 to 1000)", speed);
        return false;
    }
    return true;
}

// 安全的字符串操作
void Safe_String_Copy(char* dest, const char* src, size_t dest_size) {
    if(dest == NULL || src == NULL || dest_size == 0) {
        return;
    }

    strncpy(dest, src, dest_size - 1);
    dest[dest_size - 1] = '\0';  // 确保字符串结束
}

// 数组边界检查
bool Safe_Array_Access(uint8_t* array, size_t array_size, size_t index) {
    if(array == NULL || index >= array_size) {
        ERROR_PRINTF("Array access out of bounds: index=%u, size=%u", index, array_size);
        return false;
    }
    return true;
}
```

#### 资源管理
```c
// RAII风格的资源管理
typedef struct {
    uint8_t* buffer;
    size_t size;
    bool allocated;
} Buffer_Manager_t;

Buffer_Manager_t* Buffer_Create(size_t size) {
    Buffer_Manager_t* manager = malloc(sizeof(Buffer_Manager_t));
    if(manager == NULL) return NULL;

    manager->buffer = malloc(size);
    if(manager->buffer == NULL) {
        free(manager);
        return NULL;
    }

    manager->size = size;
    manager->allocated = true;
    return manager;
}

void Buffer_Destroy(Buffer_Manager_t* manager) {
    if(manager != NULL && manager->allocated) {
        free(manager->buffer);
        manager->buffer = NULL;
        manager->allocated = false;
        free(manager);
    }
}
```

---

*本开发规范文档为STM32F4嵌入式项目提供了完整的编码标准、接口设计、调试技巧和最佳实践指南，帮助开发者建立规范化的开发流程，提高代码质量和项目可维护性。*
```

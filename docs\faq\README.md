# STM32F4 智能小车常见问题解答 (FAQ)

## 概述

本文档收集了STM32F4智能小车开发过程中的常见问题、错误信息和解决方案，帮助开发者快速定位和解决问题。

### 问题分类
- **编译问题**: 代码编译和链接错误
- **硬件问题**: 硬件连接和配置问题
- **运行时问题**: 程序运行时的异常和错误
- **性能问题**: 系统性能和优化相关
- **调试问题**: 调试工具和方法相关

## 编译问题

### Q1: 编译时出现"undefined reference"错误
**问题描述**: 
```
undefined reference to `Motor_Set_Speed'
undefined reference to `PID_Init'
```

**原因分析**: 
- 函数声明与定义不匹配
- 源文件未正确添加到项目中
- 头文件包含路径错误

**解决方案**:
```c
// 1. 检查函数声明是否正确
// 在头文件中确保有正确的函数声明
void Motor_Set_Speed(MOTOR* motor, int speed);
void PID_Init(void);

// 2. 检查源文件是否添加到项目
// 确保相关的.c文件已添加到Makefile或IDE项目中

// 3. 检查头文件包含
#include "MyDefine.h"  // 确保包含了统一头文件
```

### Q2: 编译时出现"conflicting types"错误
**问题描述**:
```
error: conflicting types for 'Motor_Init'
note: previous declaration of 'Motor_Init' was here
```

**原因分析**:
- 函数在不同地方有不同的声明
- 头文件重复包含导致重定义

**解决方案**:
```c
// 1. 检查头文件保护
#ifndef __MOTOR_DRIVER_H__
#define __MOTOR_DRIVER_H__
// 头文件内容
#endif

// 2. 统一函数声明
// 确保所有地方的函数声明完全一致
void Motor_Init(void);  // 在所有地方保持一致

// 3. 避免重复包含
// 使用统一的MyDefine.h管理所有包含
```

### Q3: 编译时出现内存不足错误
**问题描述**:
```
region `FLASH' overflowed by 1024 bytes
region `RAM' overflowed by 512 bytes
```

**解决方案**:
```c
// 1. 优化代码大小
// 移除未使用的代码和变量
#ifdef DEBUG
    // 调试代码只在调试时编译
    printf("Debug info: %d\n", value);
#endif

// 2. 优化数据结构
// 使用合适的数据类型
uint8_t small_value;    // 而不是 int small_value;
uint16_t medium_value;  // 而不是 int medium_value;

// 3. 使用动态内存分配
uint8_t* buffer = malloc(size);
// 使用完后释放
free(buffer);

// 4. 调整链接脚本
// 在.ld文件中增加内存大小（如果硬件支持）
```

## 硬件问题

### Q4: 电机不转动
**问题描述**: 调用Motor_Set_Speed()后电机没有反应

**排查步骤**:
```c
// 1. 检查硬件连接
void Motor_Hardware_Check(void)
{
    printf("=== Motor Hardware Check ===\n");
    
    // 检查PWM输出
    printf("Testing PWM output...\n");
    HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_1);
    HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_4);
    
    // 检查方向控制引脚
    printf("Testing direction pins...\n");
    HAL_GPIO_WritePin(GPIOE, GPIO_PIN_2, GPIO_PIN_SET);   // IN1
    HAL_GPIO_WritePin(GPIOE, GPIO_PIN_3, GPIO_PIN_RESET); // IN2
    HAL_Delay(1000);
    
    HAL_GPIO_WritePin(GPIOE, GPIO_PIN_2, GPIO_PIN_RESET); // IN1
    HAL_GPIO_WritePin(GPIOE, GPIO_PIN_3, GPIO_PIN_SET);   // IN2
    HAL_Delay(1000);
    
    printf("Hardware check complete.\n");
}

// 2. 检查电源供应
// 确保电机驱动板有足够的电源供应
// 检查电源指示灯是否正常

// 3. 检查死区设置
// 死区值可能设置过大
left_motor.dead_band_speed = 50;  // 降低死区值
right_motor.dead_band_speed = 50;
```

### Q5: 编码器计数不准确
**问题描述**: 编码器计数值异常或不变化

**解决方案**:
```c
// 1. 检查编码器连接
void Encoder_Connection_Check(void)
{
    printf("=== Encoder Connection Check ===\n");
    
    // 读取原始计数值
    uint32_t left_count = __HAL_TIM_GET_COUNTER(&htim3);
    uint32_t right_count = __HAL_TIM_GET_COUNTER(&htim4);
    
    printf("Left encoder raw count: %lu\n", left_count);
    printf("Right encoder raw count: %lu\n", right_count);
    
    // 手动转动电机，观察计数变化
    printf("Manually rotate motors and observe count changes...\n");
    
    for(int i = 0; i < 10; i++) {
        HAL_Delay(1000);
        left_count = __HAL_TIM_GET_COUNTER(&htim3);
        right_count = __HAL_TIM_GET_COUNTER(&htim4);
        printf("Count[%d]: Left=%lu, Right=%lu\n", i, left_count, right_count);
    }
}

// 2. 检查定时器配置
// 确保定时器配置为编码器模式
// TIM3和TIM4应该配置为Encoder Mode

// 3. 校准编码器参数
#define ENCODER_PPR 1040  // 确保PPR值正确
#define WHEEL_CIRCUMFERENCE 20.42f  // 确保轮子周长正确
```

### Q6: I2C通信失败
**问题描述**: OLED显示不正常或MPU6050读取失败

**解决方案**:
```c
// 1. I2C总线扫描
void I2C_Bus_Scan(void)
{
    printf("=== I2C Bus Scan ===\n");
    
    for(uint8_t addr = 0x08; addr < 0x78; addr++) {
        if(HAL_I2C_IsDeviceReady(&hi2c1, addr << 1, 3, 100) == HAL_OK) {
            printf("Device found at address: 0x%02X\n", addr);
        }
    }
    
    printf("I2C scan complete.\n");
}

// 2. 检查上拉电阻
// 确保SDA和SCL线上有4.7kΩ上拉电阻

// 3. 检查I2C时钟频率
// 降低I2C时钟频率到100kHz
// 在CubeMX中设置I2C Clock Speed为100000

// 4. 增加重试机制
HAL_StatusTypeDef I2C_Write_Retry(I2C_HandleTypeDef *hi2c, uint16_t DevAddress, 
                                  uint8_t *pData, uint16_t Size)
{
    HAL_StatusTypeDef status;
    int retry_count = 3;
    
    while(retry_count--) {
        status = HAL_I2C_Master_Transmit(hi2c, DevAddress, pData, Size, 1000);
        if(status == HAL_OK) {
            return HAL_OK;
        }
        HAL_Delay(10);
    }
    
    return status;
}
```

## 运行时问题

### Q7: 系统运行一段时间后死机
**问题描述**: 程序运行一段时间后停止响应

**排查方法**:
```c
// 1. 检查栈溢出
void Stack_Overflow_Check(void)
{
    // 在main函数开始处填充栈空间
    extern uint32_t _estack;
    extern uint32_t _Min_Stack_Size;
    
    uint32_t *stack_ptr = &_estack - (uint32_t)&_Min_Stack_Size/4;
    
    // 填充魔数
    for(int i = 0; i < 100; i++) {
        stack_ptr[i] = 0xDEADBEEF;
    }
    
    // 定期检查魔数是否被覆盖
    // 在定时器中断中调用
    for(int i = 0; i < 100; i++) {
        if(stack_ptr[i] != 0xDEADBEEF) {
            printf("Stack overflow detected at position %d!\n", i);
            Error_Handler();
        }
    }
}

// 2. 检查内存泄漏
void Memory_Leak_Check(void)
{
    static size_t last_free_heap = 0;
    size_t current_free_heap = xPortGetFreeHeapSize();
    
    if(last_free_heap > 0 && current_free_heap < last_free_heap) {
        printf("Memory usage increased: %u bytes\n", 
               last_free_heap - current_free_heap);
    }
    
    last_free_heap = current_free_heap;
}

// 3. 看门狗检测
void Watchdog_Init(void)
{
    // 初始化独立看门狗
    hiwdg.Instance = IWDG;
    hiwdg.Init.Prescaler = IWDG_PRESCALER_64;
    hiwdg.Init.Reload = 4095;
    
    if(HAL_IWDG_Init(&hiwdg) != HAL_OK) {
        Error_Handler();
    }
}

void Watchdog_Feed(void)
{
    // 在主循环中定期喂狗
    HAL_IWDG_Refresh(&hiwdg);
}
```

### Q8: PID控制效果不佳
**问题描述**: PID控制器响应慢或振荡严重

**调优方法**:
```c
// 1. PID参数自动调优
void PID_Auto_Tune(PID_T* pid)
{
    printf("=== PID Auto Tuning ===\n");
    
    // Ziegler-Nichols方法
    float Ku = 0.0f;  // 临界增益
    float Tu = 0.0f;  // 临界周期
    
    // 步骤1: 找到临界增益
    pid->ki = 0.0f;
    pid->kd = 0.0f;
    
    for(float kp = 0.1f; kp < 10.0f; kp += 0.1f) {
        pid->kp = kp;
        
        // 测试系统响应
        bool oscillating = Test_System_Oscillation(pid);
        
        if(oscillating) {
            Ku = kp;
            Tu = Measure_Oscillation_Period(pid);
            break;
        }
    }
    
    if(Ku > 0 && Tu > 0) {
        // 计算PID参数
        pid->kp = 0.6f * Ku;
        pid->ki = 2.0f * pid->kp / Tu;
        pid->kd = pid->kp * Tu / 8.0f;
        
        printf("Auto-tuned parameters: Kp=%.3f, Ki=%.3f, Kd=%.3f\n",
               pid->kp, pid->ki, pid->kd);
    } else {
        printf("Auto-tuning failed. Using default parameters.\n");
        pid->kp = 0.8f;
        pid->ki = 0.1f;
        pid->kd = 0.05f;
    }
}

// 2. 手动调优指南
void PID_Manual_Tune_Guide(void)
{
    printf("=== PID Manual Tuning Guide ===\n");
    printf("1. Set Ki=0, Kd=0, increase Kp until oscillation\n");
    printf("2. Reduce Kp to 50%% of oscillation point\n");
    printf("3. Increase Ki to reduce steady-state error\n");
    printf("4. Add small Kd to reduce overshoot\n");
    printf("Current parameters: Kp=%.3f, Ki=%.3f, Kd=%.3f\n",
           pid_speed_left.kp, pid_speed_left.ki, pid_speed_left.kd);
}

// 3. PID性能评估
void PID_Performance_Evaluation(void)
{
    float rise_time = 0.0f;      // 上升时间
    float settling_time = 0.0f;  // 稳定时间
    float overshoot = 0.0f;      // 超调量
    float steady_error = 0.0f;   // 稳态误差
    
    // 测量系统响应特性
    // 实现具体的测量逻辑...
    
    printf("PID Performance:\n");
    printf("Rise time: %.2fs\n", rise_time);
    printf("Settling time: %.2fs\n", settling_time);
    printf("Overshoot: %.1f%%\n", overshoot);
    printf("Steady-state error: %.2f\n", steady_error);
}
```

### Q9: 串口通信数据丢失
**问题描述**: 串口接收数据不完整或丢失

**解决方案**:
```c
// 1. 使用DMA和环形缓冲区
#define UART_RX_BUFFER_SIZE 256
uint8_t uart_rx_buffer[UART_RX_BUFFER_SIZE];
RingBuffer uart_ring_buffer;

void UART_DMA_Init(void)
{
    // 初始化环形缓冲区
    RingBuffer_Init(&uart_ring_buffer, uart_rx_buffer, UART_RX_BUFFER_SIZE);
    
    // 启动DMA接收
    HAL_UART_Receive_DMA(&huart1, uart_rx_buffer, UART_RX_BUFFER_SIZE);
}

// 2. 处理DMA半满和全满中断
void HAL_UART_RxHalfCpltCallback(UART_HandleTypeDef *huart)
{
    if(huart == &huart1) {
        // 处理前半部分数据
        RingBuffer_Write(&uart_ring_buffer, uart_rx_buffer, UART_RX_BUFFER_SIZE/2);
    }
}

void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if(huart == &huart1) {
        // 处理后半部分数据
        RingBuffer_Write(&uart_ring_buffer, 
                        uart_rx_buffer + UART_RX_BUFFER_SIZE/2, 
                        UART_RX_BUFFER_SIZE/2);
    }
}

// 3. 数据完整性检查
bool UART_Data_Integrity_Check(uint8_t* data, uint16_t length)
{
    // 简单的校验和检查
    uint8_t checksum = 0;
    for(int i = 0; i < length - 1; i++) {
        checksum += data[i];
    }
    
    return (checksum == data[length - 1]);
}
```

## 性能问题

### Q10: 系统响应速度慢
**问题描述**: 系统整体响应速度不够快

**优化方案**:
```c
// 1. 任务优先级优化
void Task_Priority_Optimization(void)
{
    // 高优先级任务 (每5ms执行)
    static uint32_t high_priority_timer = 0;
    if(HAL_GetTick() - high_priority_timer >= 5) {
        Encoder_Task();     // 编码器读取
        Motor_Task();       // 电机控制
        high_priority_timer = HAL_GetTick();
    }
    
    // 中优先级任务 (每20ms执行)
    static uint32_t medium_priority_timer = 0;
    if(HAL_GetTick() - medium_priority_timer >= 20) {
        PID_Task();         // PID控制
        Gray_Task();        // 灰度传感器
        medium_priority_timer = HAL_GetTick();
    }
    
    // 低优先级任务 (每100ms执行)
    static uint32_t low_priority_timer = 0;
    if(HAL_GetTick() - low_priority_timer >= 100) {
        Oled_Task();        // OLED显示
        Uart_Task();        // 串口通信
        low_priority_timer = HAL_GetTick();
    }
}

// 2. 代码优化
// 使用查表法替代复杂计算
const float sin_table[360] = { /* 预计算的正弦值 */ };
const float cos_table[360] = { /* 预计算的余弦值 */ };

float fast_sin(float angle_deg)
{
    int index = (int)(angle_deg + 0.5f) % 360;
    if(index < 0) index += 360;
    return sin_table[index];
}

// 3. 中断优化
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
    if(htim == &htim2) {
        // 中断中只做最必要的操作
        measure_timer5ms++;
        key_timer10ms++;
        led_timer1000ms++;
        
        // 设置标志位，在主循环中处理
        if(measure_timer5ms >= 5) {
            measure_timer5ms = 0;
            encoder_update_flag = true;
        }
    }
}
```

### Q11: 内存使用率过高
**问题描述**: 系统内存使用率接近100%

**优化方案**:
```c
// 1. 内存使用分析
void Memory_Usage_Analysis(void)
{
    printf("=== Memory Usage Analysis ===\n");
    
    // 栈使用情况
    extern uint32_t _estack;
    extern uint32_t _Min_Stack_Size;
    uint32_t stack_size = (uint32_t)&_Min_Stack_Size;
    uint32_t stack_used = &_estack - (uint32_t*)__get_MSP();
    
    printf("Stack: %lu/%lu bytes (%.1f%%)\n", 
           stack_used, stack_size, (float)stack_used/stack_size*100);
    
    // 堆使用情况
    #ifdef configUSE_FREERTOS
    size_t heap_free = xPortGetFreeHeapSize();
    size_t heap_total = configTOTAL_HEAP_SIZE;
    size_t heap_used = heap_total - heap_free;
    
    printf("Heap: %u/%u bytes (%.1f%%)\n", 
           heap_used, heap_total, (float)heap_used/heap_total*100);
    #endif
    
    // 全局变量使用情况
    extern uint32_t _sdata, _edata, _sbss, _ebss;
    uint32_t data_size = &_edata - &_sdata;
    uint32_t bss_size = &_ebss - &_sbss;
    
    printf("Data section: %lu bytes\n", data_size);
    printf("BSS section: %lu bytes\n", bss_size);
}

// 2. 内存优化技巧
// 使用位域减少结构体大小
typedef struct {
    uint8_t motor_enable : 1;
    uint8_t pid_enable : 1;
    uint8_t sensor_ready : 1;
    uint8_t error_flag : 1;
    uint8_t reserved : 4;
} System_Flags_t;

// 使用联合体节省内存
typedef union {
    struct {
        float x, y, z;
    } vector;
    float array[3];
} Vector3D_t;

// 3. 动态内存管理
void* Safe_Malloc(size_t size)
{
    void* ptr = malloc(size);
    if(ptr == NULL) {
        printf("Memory allocation failed for %u bytes\n", size);
        // 尝试释放一些缓存
        Free_Cache_Memory();
        ptr = malloc(size);
    }
    return ptr;
}

void Free_Cache_Memory(void)
{
    // 释放非关键的缓存数据
    // 例如：历史数据缓存、临时缓冲区等
}
```

## 调试问题

### Q12: 无法进入调试模式
**问题描述**: ST-Link连接失败或无法下载程序

**解决方案**:
```
1. 硬件检查:
   - 确认ST-Link连接线完好
   - 检查SWDIO、SWCLK、GND、VCC连接
   - 确认目标板供电正常

2. 软件设置:
   - 在STM32CubeIDE中检查Debug Configuration
   - 确认芯片型号选择正确 (STM32F407VGTx)
   - 尝试不同的调试接口速度

3. 固件问题:
   - 如果程序进入低功耗模式，可能无法连接
   - 尝试按住复位键，然后连接调试器
   - 使用ST-Link Utility进行芯片擦除

4. 驱动问题:
   - 重新安装ST-Link驱动程序
   - 检查设备管理器中ST-Link设备状态
```

### Q13: printf输出不显示
**问题描述**: 使用printf函数但串口没有输出

**解决方案**:
```c
// 1. 重定向printf到UART
#include <stdio.h>

int _write(int file, char *ptr, int len)
{
    HAL_UART_Transmit(&huart1, (uint8_t*)ptr, len, 1000);
    return len;
}

// 2. 或者使用自定义printf函数
void Uart_Printf(UART_HandleTypeDef *huart, const char *format, ...)
{
    char buffer[256];
    va_list args;
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    HAL_UART_Transmit(huart, (uint8_t*)buffer, strlen(buffer), 1000);
}

// 3. 检查UART配置
// 确保波特率、数据位、停止位、校验位设置正确
// 默认配置: 115200-8-N-1

// 4. 检查串口工具设置
// 确保PC端串口工具的参数与MCU一致
```

### Q14: 变量值在调试时显示异常
**问题描述**: 调试时变量显示为乱码或异常值

**解决方案**:
```c
// 1. 检查变量作用域
// 确保变量在当前作用域内有效

// 2. 检查编译优化
// 在Debug配置中关闭编译优化
// 或者使用volatile关键字
volatile float debug_variable;

// 3. 检查内存对齐
// 确保结构体成员正确对齐
typedef struct __attribute__((packed)) {
    uint8_t flag;
    uint32_t value;
} Debug_Struct_t;

// 4. 使用调试宏
#ifdef DEBUG
    #define DEBUG_VAR(var) printf(#var " = %d\n", var)
    #define DEBUG_FLOAT(var) printf(#var " = %.2f\n", var)
#else
    #define DEBUG_VAR(var)
    #define DEBUG_FLOAT(var)
#endif

// 使用示例
int speed = 100;
DEBUG_VAR(speed);  // 输出: speed = 100
```

## 故障排除指南

### 系统启动故障排除

#### 故障现象: 系统无法启动
**排查流程**:
```c
// 1. 基础检查清单
void System_Startup_Check(void)
{
    printf("=== System Startup Diagnostic ===\n");

    // 检查时钟配置
    uint32_t sysclk = HAL_RCC_GetSysClockFreq();
    printf("System Clock: %lu Hz\n", sysclk);
    if(sysclk != 168000000) {
        printf("WARNING: Clock frequency incorrect!\n");
    }

    // 检查外设时钟
    printf("Checking peripheral clocks...\n");
    if(__HAL_RCC_GPIOA_IS_CLK_ENABLED()) printf("GPIOA: OK\n");
    if(__HAL_RCC_GPIOE_IS_CLK_ENABLED()) printf("GPIOE: OK\n");
    if(__HAL_RCC_TIM1_IS_CLK_ENABLED()) printf("TIM1: OK\n");
    if(__HAL_RCC_USART1_IS_CLK_ENABLED()) printf("USART1: OK\n");

    // 检查复位原因
    if(__HAL_RCC_GET_FLAG(RCC_FLAG_PORRST)) {
        printf("Reset cause: Power-on reset\n");
    } else if(__HAL_RCC_GET_FLAG(RCC_FLAG_PINRST)) {
        printf("Reset cause: Pin reset\n");
    } else if(__HAL_RCC_GET_FLAG(RCC_FLAG_SFTRST)) {
        printf("Reset cause: Software reset\n");
    } else if(__HAL_RCC_GET_FLAG(RCC_FLAG_IWDGRST)) {
        printf("Reset cause: Watchdog reset\n");
    }

    // 清除复位标志
    __HAL_RCC_CLEAR_RESET_FLAGS();
}

// 2. 模块初始化检查
bool Module_Init_Check(void)
{
    bool all_ok = true;

    printf("=== Module Initialization Check ===\n");

    // LED模块检查
    Led_Init();
    Led_Display(1);
    HAL_Delay(100);
    Led_Display(0);
    printf("LED: OK\n");

    // 电机模块检查
    if(Motor_Init() == HAL_OK) {
        printf("Motor: OK\n");
    } else {
        printf("Motor: FAILED\n");
        all_ok = false;
    }

    // 编码器模块检查
    if(Encoder_Init() == HAL_OK) {
        printf("Encoder: OK\n");
    } else {
        printf("Encoder: FAILED\n");
        all_ok = false;
    }

    // I2C设备检查
    if(HAL_I2C_IsDeviceReady(&hi2c1, 0x78, 3, 1000) == HAL_OK) {
        printf("OLED (0x3C): OK\n");
    } else {
        printf("OLED (0x3C): FAILED\n");
        all_ok = false;
    }

    if(HAL_I2C_IsDeviceReady(&hi2c3, 0xD0, 3, 1000) == HAL_OK) {
        printf("MPU6050 (0x68): OK\n");
    } else {
        printf("MPU6050 (0x68): FAILED\n");
        all_ok = false;
    }

    return all_ok;
}
```

#### 故障现象: 程序运行异常
**诊断工具**:
```c
// 异常处理函数增强
void HardFault_Handler(void)
{
    printf("=== HARD FAULT DETECTED ===\n");

    // 获取故障地址
    uint32_t* hardfault_args = (uint32_t*)__get_MSP();
    uint32_t stacked_r0 = hardfault_args[0];
    uint32_t stacked_r1 = hardfault_args[1];
    uint32_t stacked_r2 = hardfault_args[2];
    uint32_t stacked_r3 = hardfault_args[3];
    uint32_t stacked_r12 = hardfault_args[4];
    uint32_t stacked_lr = hardfault_args[5];
    uint32_t stacked_pc = hardfault_args[6];
    uint32_t stacked_psr = hardfault_args[7];

    printf("R0: 0x%08lX\n", stacked_r0);
    printf("R1: 0x%08lX\n", stacked_r1);
    printf("R2: 0x%08lX\n", stacked_r2);
    printf("R3: 0x%08lX\n", stacked_r3);
    printf("R12: 0x%08lX\n", stacked_r12);
    printf("LR: 0x%08lX\n", stacked_lr);
    printf("PC: 0x%08lX\n", stacked_pc);
    printf("PSR: 0x%08lX\n", stacked_psr);

    // 分析故障原因
    if(SCB->CFSR & SCB_CFSR_DIVBYZERO_Msk) {
        printf("Cause: Division by zero\n");
    }
    if(SCB->CFSR & SCB_CFSR_UNALIGNED_Msk) {
        printf("Cause: Unaligned access\n");
    }
    if(SCB->CFSR & SCB_CFSR_NOCP_Msk) {
        printf("Cause: No coprocessor\n");
    }
    if(SCB->CFSR & SCB_CFSR_INVPC_Msk) {
        printf("Cause: Invalid PC\n");
    }
    if(SCB->CFSR & SCB_CFSR_INVSTATE_Msk) {
        printf("Cause: Invalid state\n");
    }
    if(SCB->CFSR & SCB_CFSR_UNDEFINSTR_Msk) {
        printf("Cause: Undefined instruction\n");
    }

    // 系统复位
    HAL_Delay(5000);
    NVIC_SystemReset();
}
```

### 性能问题排除

#### 问题: 系统实时性不足
**性能分析工具**:
```c
// 任务执行时间统计
typedef struct {
    const char* name;
    uint32_t total_time;
    uint32_t max_time;
    uint32_t min_time;
    uint32_t call_count;
} Task_Stats_t;

Task_Stats_t task_stats[10];
uint8_t stats_count = 0;

void Task_Performance_Start(const char* task_name)
{
    // 找到或创建任务统计项
    Task_Stats_t* stats = NULL;
    for(int i = 0; i < stats_count; i++) {
        if(strcmp(task_stats[i].name, task_name) == 0) {
            stats = &task_stats[i];
            break;
        }
    }

    if(stats == NULL && stats_count < 10) {
        stats = &task_stats[stats_count++];
        stats->name = task_name;
        stats->total_time = 0;
        stats->max_time = 0;
        stats->min_time = 0xFFFFFFFF;
        stats->call_count = 0;
    }

    if(stats) {
        stats->start_time = DWT->CYCCNT;
    }
}

void Task_Performance_End(const char* task_name)
{
    uint32_t end_time = DWT->CYCCNT;

    // 找到任务统计项
    for(int i = 0; i < stats_count; i++) {
        if(strcmp(task_stats[i].name, task_name) == 0) {
            Task_Stats_t* stats = &task_stats[i];
            uint32_t execution_time = end_time - stats->start_time;

            stats->total_time += execution_time;
            stats->call_count++;

            if(execution_time > stats->max_time) {
                stats->max_time = execution_time;
            }
            if(execution_time < stats->min_time) {
                stats->min_time = execution_time;
            }
            break;
        }
    }
}

void Print_Performance_Report(void)
{
    printf("=== Performance Report ===\n");
    printf("Task Name       | Calls | Avg(us) | Max(us) | Min(us) | Total(ms)\n");
    printf("----------------|-------|---------|---------|---------|----------\n");

    for(int i = 0; i < stats_count; i++) {
        Task_Stats_t* stats = &task_stats[i];
        if(stats->call_count > 0) {
            uint32_t avg_us = (stats->total_time / stats->call_count) / (SystemCoreClock / 1000000);
            uint32_t max_us = stats->max_time / (SystemCoreClock / 1000000);
            uint32_t min_us = stats->min_time / (SystemCoreClock / 1000000);
            uint32_t total_ms = stats->total_time / (SystemCoreClock / 1000);

            printf("%-15s | %5lu | %7lu | %7lu | %7lu | %8lu\n",
                   stats->name, stats->call_count, avg_us, max_us, min_us, total_ms);
        }
    }
}

// 使用示例
void PID_Task_With_Stats(void)
{
    Task_Performance_Start("PID_Task");

    // 原始PID任务代码
    PID_Task();

    Task_Performance_End("PID_Task");
}
```

### 通信问题排除

#### 问题: 串口通信不稳定
**通信诊断工具**:
```c
// 串口通信质量测试
void UART_Communication_Test(void)
{
    printf("=== UART Communication Test ===\n");

    uint32_t test_count = 1000;
    uint32_t success_count = 0;
    uint32_t error_count = 0;

    for(uint32_t i = 0; i < test_count; i++) {
        // 发送测试数据
        char test_data[32];
        sprintf(test_data, "TEST_%04lu\r\n", i);

        HAL_StatusTypeDef status = HAL_UART_Transmit(&huart1,
                                                    (uint8_t*)test_data,
                                                    strlen(test_data),
                                                    1000);

        if(status == HAL_OK) {
            success_count++;
        } else {
            error_count++;
            printf("TX Error at packet %lu\n", i);
        }

        HAL_Delay(10);

        // 显示进度
        if(i % 100 == 0) {
            printf("Progress: %lu/%lu (%.1f%%)\n",
                   i, test_count, (float)i/test_count*100);
        }
    }

    printf("Test Results:\n");
    printf("Total packets: %lu\n", test_count);
    printf("Successful: %lu (%.2f%%)\n", success_count, (float)success_count/test_count*100);
    printf("Errors: %lu (%.2f%%)\n", error_count, (float)error_count/test_count*100);

    if(error_count == 0) {
        printf("Communication quality: EXCELLENT\n");
    } else if(error_count < test_count * 0.01f) {
        printf("Communication quality: GOOD\n");
    } else if(error_count < test_count * 0.05f) {
        printf("Communication quality: FAIR\n");
    } else {
        printf("Communication quality: POOR\n");
    }
}

// I2C通信诊断
void I2C_Communication_Diagnosis(void)
{
    printf("=== I2C Communication Diagnosis ===\n");

    // 测试不同的I2C速度
    uint32_t speeds[] = {100000, 400000, 1000000}; // 100kHz, 400kHz, 1MHz

    for(int speed_idx = 0; speed_idx < 3; speed_idx++) {
        printf("Testing I2C speed: %lu Hz\n", speeds[speed_idx]);

        // 重新配置I2C速度
        hi2c1.Init.ClockSpeed = speeds[speed_idx];
        HAL_I2C_Init(&hi2c1);

        // 测试设备响应
        uint8_t devices_found = 0;
        for(uint8_t addr = 0x08; addr < 0x78; addr++) {
            if(HAL_I2C_IsDeviceReady(&hi2c1, addr << 1, 1, 100) == HAL_OK) {
                devices_found++;
            }
        }

        printf("Devices found: %u\n", devices_found);

        // 测试数据传输
        uint8_t test_data = 0x55;
        HAL_StatusTypeDef status = HAL_I2C_Master_Transmit(&hi2c1, 0x78, &test_data, 1, 1000);
        printf("Test transmission: %s\n", (status == HAL_OK) ? "OK" : "FAILED");

        printf("---\n");
    }

    // 恢复默认速度
    hi2c1.Init.ClockSpeed = 100000;
    HAL_I2C_Init(&hi2c1);
}
```

## 开发技巧和最佳实践

### 代码调试技巧

#### 1. 条件编译调试
```c
// 调试级别定义
#define DEBUG_LEVEL_NONE    0
#define DEBUG_LEVEL_ERROR   1
#define DEBUG_LEVEL_WARNING 2
#define DEBUG_LEVEL_INFO    3
#define DEBUG_LEVEL_DEBUG   4

#ifndef DEBUG_LEVEL
#define DEBUG_LEVEL DEBUG_LEVEL_INFO
#endif

// 调试宏定义
#if DEBUG_LEVEL >= DEBUG_LEVEL_ERROR
#define DEBUG_ERROR(fmt, ...) printf("[ERROR] " fmt "\r\n", ##__VA_ARGS__)
#else
#define DEBUG_ERROR(fmt, ...)
#endif

#if DEBUG_LEVEL >= DEBUG_LEVEL_WARNING
#define DEBUG_WARNING(fmt, ...) printf("[WARN]  " fmt "\r\n", ##__VA_ARGS__)
#else
#define DEBUG_WARNING(fmt, ...)
#endif

#if DEBUG_LEVEL >= DEBUG_LEVEL_INFO
#define DEBUG_INFO(fmt, ...) printf("[INFO]  " fmt "\r\n", ##__VA_ARGS__)
#else
#define DEBUG_INFO(fmt, ...)
#endif

#if DEBUG_LEVEL >= DEBUG_LEVEL_DEBUG
#define DEBUG_DEBUG(fmt, ...) printf("[DEBUG] " fmt "\r\n", ##__VA_ARGS__)
#else
#define DEBUG_DEBUG(fmt, ...)
#endif

// 使用示例
void Motor_Set_Speed_Debug(MOTOR* motor, int speed)
{
    DEBUG_DEBUG("Motor_Set_Speed called with speed=%d", speed);

    if(motor == NULL) {
        DEBUG_ERROR("Motor pointer is NULL!");
        return;
    }

    if(abs(speed) > 1000) {
        DEBUG_WARNING("Speed %d exceeds maximum, clamping to ±1000", speed);
        speed = (speed > 0) ? 1000 : -1000;
    }

    DEBUG_INFO("Setting motor speed to %d", speed);
    Motor_Set_Speed(motor, speed);
}
```

#### 2. 运行时断言
```c
// 运行时断言宏
#ifdef DEBUG
#define RUNTIME_ASSERT(condition, message) \
    do { \
        if(!(condition)) { \
            printf("ASSERTION FAILED: %s\n", message); \
            printf("File: %s, Line: %d\n", __FILE__, __LINE__); \
            printf("Function: %s\n", __FUNCTION__); \
            while(1) { \
                Led_Display(1); \
                HAL_Delay(100); \
                Led_Display(0); \
                HAL_Delay(100); \
            } \
        } \
    } while(0)
#else
#define RUNTIME_ASSERT(condition, message)
#endif

// 使用示例
void PID_Calculate_Safe(PID_T* pid, float current_value)
{
    RUNTIME_ASSERT(pid != NULL, "PID pointer cannot be NULL");
    RUNTIME_ASSERT(pid->kp >= 0, "PID Kp must be non-negative");
    RUNTIME_ASSERT(pid->ki >= 0, "PID Ki must be non-negative");
    RUNTIME_ASSERT(pid->kd >= 0, "PID Kd must be non-negative");
    RUNTIME_ASSERT(!isnan(current_value), "Current value cannot be NaN");
    RUNTIME_ASSERT(!isinf(current_value), "Current value cannot be infinite");

    // 执行PID计算
    pid_calculate_incremental(pid, current_value);
}
```

### 性能优化技巧

#### 1. 内存池管理
```c
// 简单内存池实现
#define MEMORY_POOL_SIZE 1024
#define BLOCK_SIZE 64
#define MAX_BLOCKS (MEMORY_POOL_SIZE / BLOCK_SIZE)

static uint8_t memory_pool[MEMORY_POOL_SIZE];
static bool block_used[MAX_BLOCKS];
static uint8_t free_blocks = MAX_BLOCKS;

void* Memory_Pool_Alloc(void)
{
    for(int i = 0; i < MAX_BLOCKS; i++) {
        if(!block_used[i]) {
            block_used[i] = true;
            free_blocks--;
            return &memory_pool[i * BLOCK_SIZE];
        }
    }
    return NULL; // 内存池已满
}

void Memory_Pool_Free(void* ptr)
{
    if(ptr >= (void*)memory_pool && ptr < (void*)(memory_pool + MEMORY_POOL_SIZE)) {
        int block_index = ((uint8_t*)ptr - memory_pool) / BLOCK_SIZE;
        if(block_used[block_index]) {
            block_used[block_index] = false;
            free_blocks++;
        }
    }
}

uint8_t Memory_Pool_Get_Free_Blocks(void)
{
    return free_blocks;
}
```

#### 2. 循环展开优化
```c
// 原始循环
void Array_Process_Original(float* array, int size)
{
    for(int i = 0; i < size; i++) {
        array[i] = array[i] * 2.0f + 1.0f;
    }
}

// 循环展开优化
void Array_Process_Optimized(float* array, int size)
{
    int i = 0;

    // 4路展开
    for(; i < size - 3; i += 4) {
        array[i]     = array[i]     * 2.0f + 1.0f;
        array[i + 1] = array[i + 1] * 2.0f + 1.0f;
        array[i + 2] = array[i + 2] * 2.0f + 1.0f;
        array[i + 3] = array[i + 3] * 2.0f + 1.0f;
    }

    // 处理剩余元素
    for(; i < size; i++) {
        array[i] = array[i] * 2.0f + 1.0f;
    }
}
```

---

*本FAQ文档提供了全面的问题诊断和解决方案，涵盖了从基础硬件问题到高级性能优化的各个方面。建议开发者根据具体问题选择相应的诊断方法和解决方案。*

# STM32F4 PID控制系统专题详解

## 概述

PID控制系统是本STM32F4智能小车工程的核心控制算法，采用多环路级联控制架构，实现了精确的速度控制、角度控制和循迹控制。本文档将深入解析PID控制理论、实现原理、参数调节方法和实际应用案例，为嵌入式控制系统开发提供完整的技术指南。

### 系统控制架构
```
目标设定 → 外环控制(角度/循迹) → 内环控制(速度) → 电机驱动 → 实际输出
    ↑                                                        ↓
    └─────────────── 反馈控制环路 ←─────────────────────────────┘
```

## PID控制理论基础

### 1. PID控制器数学模型

PID控制器是一种线性控制器，它根据给定值与实际输出值构成控制偏差，将偏差的比例(P)、积分(I)和微分(D)通过线性组合构成控制量，对被控对象进行控制。

#### 连续时间PID公式
```
u(t) = Kp·e(t) + Ki·∫e(t)dt + Kd·de(t)/dt
```

其中：
- `u(t)`: 控制器输出
- `e(t)`: 控制偏差 = 设定值 - 测量值
- `Kp`: 比例增益
- `Ki`: 积分增益  
- `Kd`: 微分增益

#### 离散时间PID公式

**位置式PID**
```
u(k) = Kp·e(k) + Ki·∑e(i) + Kd·[e(k) - e(k-1)]
```

**增量式PID**
```
Δu(k) = Kp·[e(k) - e(k-1)] + Ki·e(k) + Kd·[e(k) - 2e(k-1) + e(k-2)]
u(k) = u(k-1) + Δu(k)
```

### 2. PID各分量作用机制

#### 比例分量(P)
- **作用**: 产生与当前偏差成正比的控制作用
- **特点**: 响应快速，但存在稳态误差
- **影响**: Kp增大 → 响应加快，但易产生超调和振荡

#### 积分分量(I)  
- **作用**: 消除稳态误差，提高系统精度
- **特点**: 对历史偏差进行累积
- **影响**: Ki增大 → 消除稳态误差，但可能引起积分饱和

#### 微分分量(D)
- **作用**: 预测偏差变化趋势，提前进行控制
- **特点**: 改善动态性能，减少超调
- **影响**: Kd增大 → 减少超调，但对噪声敏感

### 3. 位置式PID vs 增量式PID

| 特性 | 位置式PID | 增量式PID |
|------|-----------|-----------|
| **输出形式** | 绝对位置值 | 增量值 |
| **积分项** | 累积所有历史误差 | 仅当前误差 |
| **稳定性** | 容易积分饱和 | 不易积分饱和 |
| **适用场景** | 位置控制、温度控制 | 速度控制、增量控制 |
| **计算复杂度** | 较高 | 较低 |
| **抗干扰性** | 较差 | 较好 |

## 本工程PID控制系统架构

### 1. 多环路控制架构

本工程采用**双环级联控制**架构，包含以下控制环路：

```
┌─────────────────────────────────────────────────────────┐
│                    外环控制层                            │
├─────────────────────┬───────────────────────────────────┤
│    角度环控制        │         循迹环控制                │
│  (位置式PID)        │       (位置式PID)                │
│                    │                                   │
│ 目标角度 → PID → 差速 │ 循迹误差 → PID → 差速              │
└─────────────────────┴───────────────────────────────────┘
                            ↓
┌─────────────────────────────────────────────────────────┐
│                    内环控制层                            │
├─────────────────────┬───────────────────────────────────┤
│   左轮速度环         │        右轮速度环                  │
│  (增量式PID)        │      (增量式PID)                 │
│                    │                                   │
│ 目标速度 → PID → PWM │ 目标速度 → PID → PWM              │
└─────────────────────┴───────────────────────────────────┘
```

### 2. PID控制器实例配置

#### 速度环PID控制器
```c
// 左轮速度环参数
PidParams_t pid_params_left = {
    .kp = 8.0f,        // 比例系数：响应速度
    .ki = 5.0f,        // 积分系数：消除稳态误差
    .kd = 0.0f,        // 微分系数：减少超调
    .out_min = -999.0f,
    .out_max = 999.0f,
};

// 右轮速度环参数
PidParams_t pid_params_right = {
    .kp = 8.0f,        
    .ki = 5.0f,        
    .kd = 0.0f,        
    .out_min = -999.0f,
    .out_max = 999.0f,
};
```

**参数分析**：
- **Kp=8.0**: 适中的比例系数，保证快速响应
- **Ki=5.0**: 较大的积分系数，快速消除速度误差
- **Kd=0.0**: 不使用微分项，避免编码器噪声影响

#### 角度环PID控制器
```c
// 前进角度环参数
PidParams_t pid_params_angle = {
    .kp = 20.0f,       // 较大比例系数：快速角度纠正
    .ki = 1.0f,        // 小积分系数：避免积分饱和
    .kd = 0.0f,        // 不使用微分项
    .out_min = -999.0f,
    .out_max = 999.0f,
};

// 返回角度环参数
PidParams_t pid_params_angle_back = {
    .kp = 84.0f,       // 更大比例系数：快速转向
    .ki = 0.05f,       // 极小积分系数：防止积分饱和
    .kd = 100.0f,      // 大微分系数：减少转向超调
    .out_min = -999.0f,
    .out_max = 999.0f,
};
```

**参数分析**：
- **前进模式**: 注重稳定性，参数相对保守
- **返回模式**: 注重快速性，使用微分项减少超调

#### 循迹环PID控制器
```c
// 循迹环参数
PidParams_t pid_params_line = {
    .kp = 140.0f,      // 大比例系数：快速循迹纠正
    .ki = 0.0f,        // 不使用积分项：避免累积误差
    .kd = 0.0f,        // 不使用微分项：减少噪声影响
    .out_min = -999.0f,
    .out_max = 999.0f,
};
```

**参数分析**：
- **纯比例控制**: 循迹系统只使用比例控制，响应迅速
- **大比例系数**: 确保对路径偏差的快速响应

### 3. 控制流程实现

#### 主控制任务
```c
void PID_Task(void)
{
    if(pid_running == false) return;
    
    // 1. 内环：速度环控制 (增量式PID)
    output_left = pid_calculate_incremental(&pid_speed_left, left_encoder.speed_cm_s);
    output_right = pid_calculate_incremental(&pid_speed_right, right_encoder.speed_cm_s);
    
    // 2. 外环：角度环或循迹环控制 (位置式PID)
    if(pid_mode == 0) {  // 角度环控制
        if(angle_mode == 0) // 前进
            angle_output = pid_calculate_positional(&pid_angle, yaw);
        else // 返回
            angle_output = pid_calculate_positional(&pid_angle_back, yaw);
        
        // 差速转弯控制
        output_left = output_left - angle_output;
        output_right = output_right + angle_output;
    }
    else {  // 循迹环控制
        line_output = pid_calculate_positional(&pid_line, g_line_position_error);
        
        // 差速转弯控制
        output_left = output_left - line_output;
        output_right = output_right + line_output;
    }
    
    // 3. 输出限幅
    output_left = pid_constrain(output_left, pid_params_left.out_min, pid_params_left.out_max);
    output_right = pid_constrain(output_right, pid_params_right.out_min, pid_params_right.out_max);
    
    // 4. 电机驱动
    Motor_Set_Speed(&left_motor, output_left);
    Motor_Set_Speed(&right_motor, output_right);
}
```

#### 控制逻辑分析

**1. 双环级联控制**
- **内环(速度环)**: 使用增量式PID，控制电机转速
- **外环(角度/循迹环)**: 使用位置式PID，产生差速控制量

**2. 差速转弯机制**
```c
// 差速转弯公式
output_left = speed_output - turn_output;   // 左轮减速
output_right = speed_output + turn_output;  // 右轮加速
```

**3. 模式切换**
- **角度模式**: 基于IMU姿态角进行方向控制
- **循迹模式**: 基于灰度传感器进行路径跟踪

## PID参数调节指南

### 1. 参数调节原则

#### Ziegler-Nichols调节法
1. **确定临界增益Kc**: 只使用比例控制，逐渐增大Kp直到系统临界振荡
2. **测量临界周期Tc**: 记录临界振荡的周期
3. **计算PID参数**:
   - Kp = 0.6 × Kc
   - Ki = 2 × Kp / Tc  
   - Kd = Kp × Tc / 8

#### 经验调节法
1. **先调比例(P)**: 从小到大调节，观察响应速度和超调
2. **再调积分(I)**: 消除稳态误差，注意积分饱和
3. **最后调微分(D)**: 减少超调，提高稳定性

### 2. 分环路调节策略

#### 速度环调节
```c
// 调节步骤：
// 1. 设置Ki=0, Kd=0，只调节Kp
// 2. 逐渐增大Kp，观察速度响应
// 3. 当出现轻微超调时，记录Kp值
// 4. 适当减小Kp，增加Ki消除稳态误差
// 5. 如有振荡，适当增加Kd

// 推荐起始参数
float kp_start = 5.0f;   // 起始比例系数
float ki_start = 2.0f;   // 起始积分系数
float kd_start = 0.0f;   // 起始微分系数
```

#### 角度环调节
```c
// 调节步骤：
// 1. 确保速度环已调节完成
// 2. 设置较小的Kp，测试角度响应
// 3. 逐渐增大Kp，直到响应满意
// 4. 如有稳态误差，适当增加Ki
// 5. 如有超调，适当增加Kd

// 推荐起始参数
float kp_angle_start = 10.0f;  // 起始比例系数
float ki_angle_start = 0.5f;   // 起始积分系数
float kd_angle_start = 0.0f;   // 起始微分系数
```

#### 循迹环调节
```c
// 调节步骤：
// 1. 确保速度环和角度环已调节完成
// 2. 使用纯比例控制，从小Kp开始
// 3. 逐渐增大Kp，观察循迹效果
// 4. 找到响应快速且稳定的Kp值
// 5. 一般不使用积分和微分项

// 推荐起始参数
float kp_line_start = 50.0f;   // 起始比例系数
float ki_line_start = 0.0f;    // 不使用积分
float kd_line_start = 0.0f;    // 不使用微分
```

### 3. 参数调节工具和方法

#### 在线调参接口
```c
// 通过串口或OLED实时调节参数
void PID_Tune_Online(uint8_t pid_id, float kp, float ki, float kd) {
    switch(pid_id) {
        case 0: // 左轮速度环
            pid_set_params(&pid_speed_left, kp, ki, kd);
            break;
        case 1: // 右轮速度环
            pid_set_params(&pid_speed_right, kp, ki, kd);
            break;
        case 2: // 角度环
            pid_set_params(&pid_angle, kp, ki, kd);
            break;
        case 3: // 循迹环
            pid_set_params(&pid_line, kp, ki, kd);
            break;
    }
}
```

#### 参数保存和加载
```c
// 将调节好的参数保存到Flash
void PID_Params_Save(void) {
    // 保存到Flash或EEPROM
    Flash_Write_PID_Params(&pid_params_left, &pid_params_right, 
                          &pid_params_angle, &pid_params_line);
}

// 从Flash加载参数
void PID_Params_Load(void) {
    // 从Flash或EEPROM读取
    Flash_Read_PID_Params(&pid_params_left, &pid_params_right, 
                         &pid_params_angle, &pid_params_line);
}
```

## 实际应用案例分析

### 1. 直线行驶控制案例

#### 控制目标
实现小车以恒定速度直线行驶，保持方向稳定，无偏航。

#### 控制策略
```c
// 直线行驶控制实现
void Straight_Line_Control(float target_speed) {
    // 1. 设置速度环目标
    pid_set_target(&pid_speed_left, target_speed);
    pid_set_target(&pid_speed_right, target_speed);

    // 2. 设置角度环目标为0（保持当前方向）
    pid_set_target(&pid_angle, 0.0f);

    // 3. 启用角度环控制模式
    pid_mode = 0;  // 角度环控制
    angle_mode = 0; // 前进模式
    pid_running = true;
}
```

#### 参数调节过程
```c
// 第一步：调节速度环
// 初始参数：Kp=5.0, Ki=2.0, Kd=0.0
// 现象：速度响应慢，有稳态误差
// 调节：增大Kp到8.0，增大Ki到5.0
// 结果：响应快速，稳态误差消除

// 第二步：调节角度环
// 初始参数：Kp=10.0, Ki=0.5, Kd=0.0
// 现象：方向纠正慢，有轻微摆动
// 调节：增大Kp到20.0，减小Ki到1.0
// 结果：方向纠正快速，摆动减少
```

#### 效果分析
- **速度稳定性**: ±2cm/s误差范围内
- **方向稳定性**: ±2°角度误差范围内
- **响应时间**: 速度调节时间<0.5s，角度调节时间<0.3s

### 2. 循迹控制案例

#### 控制目标
实现小车沿黑线精确行驶，能够处理直线、弯道、交叉路口等复杂路况。

#### 控制策略
```c
// 循迹控制实现
void Line_Following_Control(float target_speed) {
    // 1. 设置速度环目标
    pid_set_target(&pid_speed_left, target_speed);
    pid_set_target(&pid_speed_right, target_speed);

    // 2. 循迹环目标始终为0（路径中心）
    pid_set_target(&pid_line, 0.0f);

    // 3. 启用循迹环控制模式
    pid_mode = 1;  // 循迹环控制
    pid_running = true;
}
```

#### 循迹误差计算
```c
// 基于8路灰度传感器的循迹误差计算
float Calculate_Line_Position_Error(uint8_t sensor_data[8]) {
    float weighted_sum = 0;
    float total_weight = 0;

    // 传感器权重分配（中心为0，两侧为±3.5）
    float weights[8] = {-3.5f, -2.5f, -1.5f, -0.5f, 0.5f, 1.5f, 2.5f, 3.5f};

    for(int i = 0; i < 8; i++) {
        if(sensor_data[i] == 1) {  // 检测到黑线
            weighted_sum += weights[i];
            total_weight += 1.0f;
        }
    }

    if(total_weight > 0) {
        return weighted_sum / total_weight;  // 归一化误差
    }
    return 0.0f;  // 无线检测
}
```

#### 参数调节过程
```c
// 循迹环参数调节
// 初始参数：Kp=50.0, Ki=0.0, Kd=0.0
// 现象：循迹响应慢，过弯困难
// 调节：增大Kp到100.0
// 现象：响应加快，但有振荡
// 调节：继续增大Kp到140.0，保持Ki=0, Kd=0
// 结果：响应快速，循迹精确
```

#### 效果分析
- **循迹精度**: 路径偏差<±1cm
- **过弯能力**: 能处理半径>30cm的弯道
- **速度适应**: 在10-50cm/s速度范围内稳定循迹

### 3. 转向控制案例

#### 控制目标
实现小车精确转向，包括90°转弯、180°掉头等机动动作。

#### 控制策略
```c
// 90度转向控制
void Turn_90_Degrees(bool turn_right) {
    float target_angle = turn_right ? -90.0f : 90.0f;

    // 1. 设置较低的速度
    pid_set_target(&pid_speed_left, 20.0f);
    pid_set_target(&pid_speed_right, 20.0f);

    // 2. 设置目标角度
    pid_set_target(&pid_angle_back, target_angle);

    // 3. 启用返回角度环控制
    pid_mode = 0;     // 角度环控制
    angle_mode = 1;   // 返回模式
    pid_running = true;

    // 4. 等待转向完成
    while(fabs(yaw - target_angle) > 5.0f) {
        HAL_Delay(10);
    }

    // 5. 重置角度目标
    pid_set_target(&pid_angle_back, 0.0f);
}
```

#### 参数调节过程
```c
// 返回角度环参数调节
// 初始参数：Kp=50.0, Ki=0.1, Kd=10.0
// 现象：转向慢，有超调
// 调节：增大Kp到84.0，减小Ki到0.05，增大Kd到100.0
// 结果：转向快速，超调减少，精度提高
```

#### 效果分析
- **转向精度**: ±3°角度误差
- **转向时间**: 90°转向<2s
- **稳定性**: 无明显超调和振荡

## 常见问题与故障排除

### 1. 速度环问题

#### 问题1：速度响应慢
**现象**: 设定速度后，实际速度达到目标值时间过长
**原因分析**:
- Kp过小，比例作用不足
- 电机驱动能力不足
- 编码器分辨率过低

**解决方案**:
```c
// 增大比例系数
pid_params_left.kp = 10.0f;  // 从8.0增加到10.0
pid_params_right.kp = 10.0f;

// 检查电机驱动
Motor_Set_Speed(&left_motor, 500);  // 测试最大输出
Motor_Set_Speed(&right_motor, 500);

// 验证编码器
printf("Left Encoder: %d, Right Encoder: %d\n",
       left_encoder.count, right_encoder.count);
```

#### 问题2：速度振荡
**现象**: 实际速度围绕目标值振荡
**原因分析**:
- Kp过大，系统不稳定
- Ki过大，积分饱和
- 编码器噪声干扰

**解决方案**:
```c
// 减小比例系数
pid_params_left.kp = 6.0f;   // 从8.0减少到6.0

// 减小积分系数
pid_params_left.ki = 3.0f;   // 从5.0减少到3.0

// 添加编码器滤波
float filtered_speed = 0.8f * last_speed + 0.2f * current_speed;
```

#### 问题3：稳态误差
**现象**: 速度稳定后仍有固定偏差
**原因分析**:
- Ki过小，积分作用不足
- 存在系统偏差（摩擦、重心偏移等）
- 积分限幅过小

**解决方案**:
```c
// 增大积分系数
pid_params_left.ki = 7.0f;   // 从5.0增加到7.0

// 检查积分限幅
if(pid_speed_left.integral > 100.0f) {
    pid_speed_left.integral = 100.0f;
}

// 添加前馈补偿
float feedforward = target_speed * 0.1f;
output_left += feedforward;
```

### 2. 角度环问题

#### 问题1：方向纠正慢
**现象**: 小车偏离方向后，纠正时间过长
**原因分析**:
- Kp过小，纠正力度不足
- IMU数据更新频率低
- 差速控制量不足

**解决方案**:
```c
// 增大角度环比例系数
pid_params_angle.kp = 30.0f;  // 从20.0增加到30.0

// 检查IMU更新频率
if(mpu_dmp_get_data(&pitch, &roll, &yaw) == 0) {
    // 确保IMU数据及时更新
}

// 增大差速控制范围
pid_params_angle.out_max = 1500.0f;  // 从999.0增加到1500.0
```

#### 问题2：方向超调
**现象**: 方向纠正过程中出现反向超调
**原因分析**:
- Kp过大，纠正过度
- 缺少微分控制
- 机械惯性过大

**解决方案**:
```c
// 减小比例系数
pid_params_angle.kp = 15.0f;  // 从20.0减少到15.0

// 增加微分控制
pid_params_angle.kd = 5.0f;   // 从0.0增加到5.0

// 限制最大转向速度
float max_turn_rate = 50.0f;  // 最大转向角速度
if(fabs(angle_output) > max_turn_rate) {
    angle_output = (angle_output > 0) ? max_turn_rate : -max_turn_rate;
}
```

### 3. 循迹环问题

#### 问题1：循迹精度差
**现象**: 小车无法精确跟随黑线，偏差较大
**原因分析**:
- 传感器标定不准确
- Kp过小，纠正不足
- 循迹误差计算有问题

**解决方案**:
```c
// 重新标定传感器
void Calibrate_Gray_Sensors(void) {
    // 白线标定
    printf("请将传感器置于白线上，按键确认\n");
    // 记录白线值

    // 黑线标定
    printf("请将传感器置于黑线上，按键确认\n");
    // 记录黑线值
}

// 增大循迹比例系数
pid_params_line.kp = 180.0f;  // 从140.0增加到180.0

// 优化误差计算算法
float Calculate_Line_Error_Advanced(uint8_t sensors[8]) {
    // 使用加权平均算法
    // 考虑传感器可靠性
    // 处理边界情况
}
```

#### 问题2：过弯失败
**现象**: 小车在急弯处脱离轨道
**原因分析**:
- 速度过快，转向不及
- 循迹算法对急弯适应性差
- 传感器检测范围不足

**解决方案**:
```c
// 动态速度调节
void Adaptive_Speed_Control(float line_error) {
    float base_speed = 30.0f;
    float speed_reduction = fabs(line_error) * 5.0f;
    float target_speed = base_speed - speed_reduction;

    if(target_speed < 10.0f) target_speed = 10.0f;

    pid_set_target(&pid_speed_left, target_speed);
    pid_set_target(&pid_speed_right, target_speed);
}

// 预测性循迹算法
float Predictive_Line_Following(uint8_t sensors[8]) {
    // 基于传感器模式识别弯道类型
    // 提前调整控制策略
    // 使用历史数据预测路径
}
```

### 4. 系统级问题

#### 问题1：控制系统不稳定
**现象**: 整个控制系统出现周期性振荡
**原因分析**:
- 控制周期不合适
- 多环路耦合干扰
- 硬件噪声干扰

**解决方案**:
```c
// 调整控制周期
#define PID_CONTROL_PERIOD_MS  20  // 从10ms调整到20ms

// 解耦控制环路
void Decoupled_PID_Control(void) {
    // 先执行内环控制
    Speed_Loop_Control();
    HAL_Delay(5);

    // 再执行外环控制
    if(pid_mode == 0) {
        Angle_Loop_Control();
    } else {
        Line_Loop_Control();
    }
}

// 添加软件滤波
float Low_Pass_Filter(float input, float last_output, float alpha) {
    return alpha * input + (1.0f - alpha) * last_output;
}
```

#### 问题2：参数漂移
**现象**: 调节好的参数在使用过程中效果变差
**原因分析**:
- 环境温度变化
- 电池电压下降
- 机械磨损

**解决方案**:
```c
// 自适应参数调节
void Adaptive_PID_Tuning(void) {
    // 监测系统性能指标
    float performance_index = Calculate_Performance_Index();

    if(performance_index < threshold) {
        // 自动微调参数
        Auto_Tune_Parameters();
    }
}

// 电压补偿
void Voltage_Compensation(void) {
    float battery_voltage = Get_Battery_Voltage();
    float compensation_factor = 12.0f / battery_voltage;

    // 补偿电机输出
    output_left *= compensation_factor;
    output_right *= compensation_factor;
}
```

## PID控制系统优化建议

### 1. 性能优化

#### 计算优化
```c
// 使用定点运算替代浮点运算
#define FIXED_POINT_SCALE 1000
typedef int32_t fixed_point_t;

fixed_point_t pid_calculate_fixed(PID_Fixed_T *pid, fixed_point_t current) {
    // 定点PID计算，提高运算速度
}

// 查表法实现三角函数
float sin_table[360];  // 预计算正弦表
float fast_sin(float angle) {
    int index = (int)(angle + 0.5f) % 360;
    return sin_table[index];
}
```

#### 内存优化
```c
// 使用环形缓冲区存储历史数据
#define HISTORY_SIZE 10
typedef struct {
    float data[HISTORY_SIZE];
    uint8_t index;
} History_Buffer_t;

void Add_History_Data(History_Buffer_t *buffer, float value) {
    buffer->data[buffer->index] = value;
    buffer->index = (buffer->index + 1) % HISTORY_SIZE;
}
```

### 2. 鲁棒性增强

#### 异常检测
```c
// PID输出异常检测
bool PID_Output_Check(float output) {
    static float last_output = 0;

    // 检测输出突变
    if(fabs(output - last_output) > 500.0f) {
        return false;  // 输出异常
    }

    // 检测输出饱和
    if(fabs(output) > 990.0f) {
        return false;  // 输出饱和
    }

    last_output = output;
    return true;  // 输出正常
}
```

#### 故障恢复
```c
// PID控制器故障恢复
void PID_Fault_Recovery(void) {
    // 重置PID控制器
    pid_reset(&pid_speed_left);
    pid_reset(&pid_speed_right);
    pid_reset(&pid_angle);
    pid_reset(&pid_line);

    // 恢复默认参数
    PID_Load_Default_Params();

    // 重新初始化
    PID_Init();
}
```

### 3. 调试工具

#### 数据记录
```c
// PID调试数据结构
typedef struct {
    uint32_t timestamp;
    float target;
    float current;
    float error;
    float output;
    float p_out, i_out, d_out;
} PID_Debug_Data_t;

// 记录PID调试数据
void Record_PID_Debug_Data(PID_T *pid, PID_Debug_Data_t *debug_data) {
    debug_data->timestamp = HAL_GetTick();
    debug_data->target = pid->target;
    debug_data->current = pid->current;
    debug_data->error = pid->error;
    debug_data->output = pid->out;
    debug_data->p_out = pid->p_out;
    debug_data->i_out = pid->i_out;
    debug_data->d_out = pid->d_out;
}
```

#### 实时监控
```c
// 通过串口输出PID状态
void PID_Status_Monitor(void) {
    printf("Speed_L: T=%.1f C=%.1f O=%.1f\n",
           pid_speed_left.target,
           pid_speed_left.current,
           pid_speed_left.out);

    printf("Speed_R: T=%.1f C=%.1f O=%.1f\n",
           pid_speed_right.target,
           pid_speed_right.current,
           pid_speed_right.out);

    printf("Angle: T=%.1f C=%.1f O=%.1f\n",
           pid_angle.target,
           yaw,
           angle_output);
}
```

---

*本文档全面介绍了STM32F4智能小车PID控制系统的理论基础、实现架构、参数调节方法、实际应用案例和故障排除技巧，为嵌入式控制系统开发提供了完整的技术指南和实用工具。通过系统性的学习和实践，可以掌握PID控制技术的精髓，开发出高性能的嵌入式控制系统。*

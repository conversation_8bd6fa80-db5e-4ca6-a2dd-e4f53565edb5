#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.Request0=USART1_RX
Dma.Request1=UART4_RX
Dma.Request2=UART5_RX
Dma.Request3=USART2_RX
Dma.Request4=USART3_RX
Dma.Request5=USART6_RX
Dma.RequestsNb=6
Dma.UART4_RX.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.UART4_RX.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.UART4_RX.1.Instance=DMA1_Stream2
Dma.UART4_RX.1.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UART4_RX.1.MemInc=DMA_MINC_ENABLE
Dma.UART4_RX.1.Mode=DMA_NORMAL
Dma.UART4_RX.1.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UART4_RX.1.PeriphInc=DMA_PINC_DISABLE
Dma.UART4_RX.1.Priority=DMA_PRIORITY_LOW
Dma.UART4_RX.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.UART5_RX.2.Direction=DMA_PERIPH_TO_MEMORY
Dma.UART5_RX.2.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.UART5_RX.2.Instance=DMA1_Stream0
Dma.UART5_RX.2.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UART5_RX.2.MemInc=DMA_MINC_ENABLE
Dma.UART5_RX.2.Mode=DMA_NORMAL
Dma.UART5_RX.2.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UART5_RX.2.PeriphInc=DMA_PINC_DISABLE
Dma.UART5_RX.2.Priority=DMA_PRIORITY_LOW
Dma.UART5_RX.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART1_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART1_RX.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_RX.0.Instance=DMA2_Stream2
Dma.USART1_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_RX.0.MemInc=DMA_MINC_ENABLE
Dma.USART1_RX.0.Mode=DMA_NORMAL
Dma.USART1_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_RX.0.Priority=DMA_PRIORITY_LOW
Dma.USART1_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART2_RX.3.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART2_RX.3.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART2_RX.3.Instance=DMA1_Stream5
Dma.USART2_RX.3.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART2_RX.3.MemInc=DMA_MINC_ENABLE
Dma.USART2_RX.3.Mode=DMA_NORMAL
Dma.USART2_RX.3.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART2_RX.3.PeriphInc=DMA_PINC_DISABLE
Dma.USART2_RX.3.Priority=DMA_PRIORITY_LOW
Dma.USART2_RX.3.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART3_RX.4.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART3_RX.4.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART3_RX.4.Instance=DMA1_Stream1
Dma.USART3_RX.4.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART3_RX.4.MemInc=DMA_MINC_ENABLE
Dma.USART3_RX.4.Mode=DMA_NORMAL
Dma.USART3_RX.4.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART3_RX.4.PeriphInc=DMA_PINC_DISABLE
Dma.USART3_RX.4.Priority=DMA_PRIORITY_LOW
Dma.USART3_RX.4.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART6_RX.5.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART6_RX.5.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART6_RX.5.Instance=DMA2_Stream1
Dma.USART6_RX.5.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART6_RX.5.MemInc=DMA_MINC_ENABLE
Dma.USART6_RX.5.Mode=DMA_NORMAL
Dma.USART6_RX.5.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART6_RX.5.PeriphInc=DMA_PINC_DISABLE
Dma.USART6_RX.5.Priority=DMA_PRIORITY_LOW
Dma.USART6_RX.5.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
File.Version=6
GPIO.groupedBy=Group By Peripherals
I2C1.I2C_Mode=I2C_Fast
I2C1.IPParameters=I2C_Mode
I2C2.I2C_Mode=I2C_Fast
I2C2.IPParameters=I2C_Mode
KeepUserPlacement=false
Mcu.CPN=STM32F407VGT6
Mcu.Family=STM32F4
Mcu.IP0=DMA
Mcu.IP1=I2C1
Mcu.IP10=UART4
Mcu.IP11=UART5
Mcu.IP12=USART1
Mcu.IP13=USART2
Mcu.IP14=USART3
Mcu.IP15=USART6
Mcu.IP2=I2C2
Mcu.IP3=NVIC
Mcu.IP4=RCC
Mcu.IP5=SYS
Mcu.IP6=TIM1
Mcu.IP7=TIM2
Mcu.IP8=TIM3
Mcu.IP9=TIM4
Mcu.IPNb=16
Mcu.Name=STM32F407V(E-G)Tx
Mcu.Package=LQFP100
Mcu.Pin0=PC14-OSC32_IN
Mcu.Pin1=PC15-OSC32_OUT
Mcu.Pin10=PB2
Mcu.Pin11=PE8
Mcu.Pin12=PE9
Mcu.Pin13=PE10
Mcu.Pin14=PE11
Mcu.Pin15=PE12
Mcu.Pin16=PE14
Mcu.Pin17=PB10
Mcu.Pin18=PB11
Mcu.Pin19=PD8
Mcu.Pin2=PH0-OSC_IN
Mcu.Pin20=PD9
Mcu.Pin21=PD12
Mcu.Pin22=PD13
Mcu.Pin23=PC6
Mcu.Pin24=PC7
Mcu.Pin25=PA9
Mcu.Pin26=PA10
Mcu.Pin27=PA13
Mcu.Pin28=PA14
Mcu.Pin29=PC10
Mcu.Pin3=PH1-OSC_OUT
Mcu.Pin30=PC12
Mcu.Pin31=PD2
Mcu.Pin32=PB6
Mcu.Pin33=PB7
Mcu.Pin34=VP_SYS_VS_Systick
Mcu.Pin35=VP_TIM1_VS_ClockSourceINT
Mcu.Pin36=VP_TIM2_VS_ClockSourceINT
Mcu.Pin4=PA0-WKUP
Mcu.Pin5=PA1
Mcu.Pin6=PA2
Mcu.Pin7=PA3
Mcu.Pin8=PA6
Mcu.Pin9=PA7
Mcu.PinsNb=37
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F407VGTx
MxCube.Version=6.11.1
MxDb.Version=DB.6.0.111
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA1_Stream0_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA1_Stream1_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA1_Stream2_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA1_Stream5_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream1_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream2_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:0\:0\:true\:false\:true\:false\:true\:false
NVIC.TIM2_IRQn=true\:1\:0\:true\:false\:true\:true\:true\:true
NVIC.UART4_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UART5_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART2_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART3_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART6_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA0-WKUP.GPIOParameters=GPIO_Label
PA0-WKUP.GPIO_Label=Key
PA0-WKUP.Locked=true
PA0-WKUP.Signal=GPIO_Input
PA1.Mode=Asynchronous
PA1.Signal=UART4_RX
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA2.Mode=Asynchronous
PA2.Signal=USART2_TX
PA3.Mode=Asynchronous
PA3.Signal=USART2_RX
PA6.Signal=S_TIM3_CH1
PA7.Signal=S_TIM3_CH2
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB10.Mode=I2C
PB10.Signal=I2C2_SCL
PB11.Mode=I2C
PB11.Signal=I2C2_SDA
PB2.GPIOParameters=GPIO_Label
PB2.GPIO_Label=LED
PB2.Locked=true
PB2.Signal=GPIO_Output
PB6.Mode=I2C
PB6.Signal=I2C1_SCL
PB7.Mode=I2C
PB7.Signal=I2C1_SDA
PC10.Mode=Asynchronous
PC10.Signal=UART4_TX
PC12.Mode=Asynchronous
PC12.Signal=UART5_TX
PC14-OSC32_IN.Mode=LSE-External-Oscillator
PC14-OSC32_IN.Signal=RCC_OSC32_IN
PC15-OSC32_OUT.Mode=LSE-External-Oscillator
PC15-OSC32_OUT.Signal=RCC_OSC32_OUT
PC6.Mode=Asynchronous
PC6.Signal=USART6_TX
PC7.Mode=Asynchronous
PC7.Signal=USART6_RX
PD12.Signal=S_TIM4_CH1
PD13.Signal=S_TIM4_CH2
PD2.Mode=Asynchronous
PD2.Signal=UART5_RX
PD8.Mode=Asynchronous
PD8.Signal=USART3_TX
PD9.Mode=Asynchronous
PD9.Signal=USART3_RX
PE10.GPIOParameters=GPIO_Label
PE10.GPIO_Label=BIN2
PE10.Locked=true
PE10.Signal=GPIO_Output
PE11.Signal=S_TIM1_CH2
PE12.GPIOParameters=GPIO_Label
PE12.GPIO_Label=AIN1
PE12.Locked=true
PE12.Signal=GPIO_Output
PE14.GPIOParameters=GPIO_Label
PE14.GPIO_Label=AIN2
PE14.Locked=true
PE14.Signal=GPIO_Output
PE8.GPIOParameters=GPIO_Label
PE8.GPIO_Label=BIN1
PE8.Locked=true
PE8.Signal=GPIO_Output
PE9.Signal=S_TIM1_CH1
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F407VGTx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=08_PID.ioc
ProjectManager.ProjectName=08_PID
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_USART1_UART_Init-USART1-false-HAL-true,5-MX_I2C1_Init-I2C1-false-HAL-true,6-MX_TIM1_Init-TIM1-false-HAL-true,7-MX_TIM3_Init-TIM3-false-HAL-true,8-MX_TIM4_Init-TIM4-false-HAL-true,9-MX_TIM2_Init-TIM2-false-HAL-true,10-MX_I2C2_Init-I2C2-false-HAL-true,11-MX_UART4_Init-UART4-false-HAL-true,12-MX_UART5_Init-UART5-false-HAL-true,13-MX_USART2_UART_Init-USART2-false-HAL-true,14-MX_USART3_UART_Init-USART3-false-HAL-true,15-MX_USART6_UART_Init-USART6-false-HAL-true
RCC.48MHZClocksFreq_Value=84000000
RCC.AHBFreq_Value=168000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=42000000
RCC.APB1TimFreq_Value=84000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=84000000
RCC.APB2TimFreq_Value=168000000
RCC.CortexFreq_Value=168000000
RCC.EthernetFreq_Value=168000000
RCC.FCLKCortexFreq_Value=168000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=168000000
RCC.HSE_VALUE=8000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=192000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,PLLSourceVirtual,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=168000000
RCC.PLLCLKFreq_Value=168000000
RCC.PLLM=4
RCC.PLLN=168
RCC.PLLQCLKFreq_Value=84000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=4000000
RCC.SYSCLKFreq_VALUE=168000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=384000000
RCC.VCOInputFreq_Value=2000000
RCC.VCOOutputFreq_Value=336000000
RCC.VcooutputI2S=192000000
SH.S_TIM1_CH1.0=TIM1_CH1,PWM Generation1 CH1
SH.S_TIM1_CH1.ConfNb=1
SH.S_TIM1_CH2.0=TIM1_CH2,PWM Generation2 CH2
SH.S_TIM1_CH2.ConfNb=1
SH.S_TIM3_CH1.0=TIM3_CH1,Encoder_Interface
SH.S_TIM3_CH1.ConfNb=1
SH.S_TIM3_CH2.0=TIM3_CH2,Encoder_Interface
SH.S_TIM3_CH2.ConfNb=1
SH.S_TIM4_CH1.0=TIM4_CH1,Encoder_Interface
SH.S_TIM4_CH1.ConfNb=1
SH.S_TIM4_CH2.0=TIM4_CH2,Encoder_Interface
SH.S_TIM4_CH2.ConfNb=1
TIM1.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM1.Channel-PWM\ Generation2\ CH2=TIM_CHANNEL_2
TIM1.IPParameters=Channel-PWM Generation1 CH1,Channel-PWM Generation2 CH2,Prescaler,Period
TIM1.Period=1000-1
TIM1.Prescaler=8-1
TIM2.IPParameters=Prescaler,Period
TIM2.Period=1000-1
TIM2.Prescaler=84-1
TIM3.EncoderMode=TIM_ENCODERMODE_TI12
TIM3.IPParameters=EncoderMode
TIM4.EncoderMode=TIM_ENCODERMODE_TI12
TIM4.IPParameters=EncoderMode
UART4.IPParameters=VirtualMode
UART4.VirtualMode=Asynchronous
UART5.IPParameters=VirtualMode
UART5.VirtualMode=Asynchronous
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
USART2.IPParameters=VirtualMode
USART2.VirtualMode=VM_ASYNC
USART3.IPParameters=VirtualMode
USART3.VirtualMode=VM_ASYNC
USART6.IPParameters=VirtualMode
USART6.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM1_VS_ClockSourceINT.Mode=Internal
VP_TIM1_VS_ClockSourceINT.Signal=TIM1_VS_ClockSourceINT
VP_TIM2_VS_ClockSourceINT.Mode=Internal
VP_TIM2_VS_ClockSourceINT.Signal=TIM2_VS_ClockSourceINT
board=custom

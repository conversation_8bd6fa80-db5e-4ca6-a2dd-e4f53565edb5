# STM32F4 App层应用模块详解

## 概述

App层是整个系统的业务逻辑层，负责实现具体的应用功能和业务逻辑。该层调用Driver层提供的硬件接口，实现传感器数据处理、控制算法执行、人机交互等高级功能。App层采用模块化设计，每个模块专注于特定的业务功能，通过数据共享和函数调用实现模块间协作。

### 设计理念
- **业务导向**: 专注于实现具体的应用功能和业务逻辑
- **数据驱动**: 基于传感器数据进行决策和控制
- **模块协作**: 通过全局变量和函数调用实现模块间通信
- **实时响应**: 保证控制系统的实时性和稳定性

## App层架构

### 文件组织结构
```
User/App/
├── pid_app.c/.h             # PID控制算法应用
├── encoder_app.c/.h         # 编码器数据处理应用
├── mpu6050_app.c/.h         # MPU6050姿态解算应用
├── gray_app.c/.h            # 灰度传感器循迹应用
├── oled_app.c/.h            # OLED显示控制应用
├── key_app.c/.h             # 按键交互处理应用
├── led_app.c/.h             # LED状态指示应用
├── motor_app.c/.h           # 电机控制应用
└── uart_app.c/.h            # 串口通信应用
```

### 应用层数据流向
```
传感器数据采集 → 数据处理与融合 → 控制算法计算 → 执行器控制 → 状态反馈
     ↓              ↓              ↓            ↓           ↓
编码器/MPU6050   → 速度/角度计算 → PID控制器 → 电机驱动 → 显示/通信
灰度传感器      → 位置误差计算 → 循迹算法  → 差速控制 → 状态指示
按键输入        → 模式切换     → 参数调整  → 系统控制 → 用户反馈
```

## 核心应用模块详解

### 1. PID控制应用模块 (pid_app)

#### 功能概述
PID控制应用是整个系统的核心控制模块，实现了多环路PID控制系统，包括速度环、角度环和循迹环，为小车提供精确的运动控制。

#### 控制系统架构
```
外环控制 (位置/角度) → 内环控制 (速度) → 电机输出
     ↓                    ↓              ↓
角度环/循迹环         → 速度环PID    → PWM控制
```

#### 核心数据结构

**PID参数结构体**
```c
typedef struct
{
    float kp;          // 比例系数
    float ki;          // 积分系数  
    float kd;          // 微分系数
    float out_min;     // 输出最小值
    float out_max;     // 输出最大值
} PidParams_t;
```

**PID控制器实例**
```c
PID_T pid_speed_left;   // 左轮速度环
PID_T pid_speed_right;  // 右轮速度环
PID_T pid_angle;        // 前进角度环
PID_T pid_angle_back;   // 返回角度环
PID_T pid_line;         // 循迹环
```

#### PID参数配置

**速度环参数** (增量式PID)
```c
PidParams_t pid_params_left = {
    .kp = 8.0f,        // 比例系数
    .ki = 5.0f,        // 积分系数
    .kd = 0.0f,        // 微分系数
    .out_min = -999.0f,
    .out_max = 999.0f,
};
```

**角度环参数** (位置式PID)
```c
// 前进角度环 - 响应较慢，稳定性好
PidParams_t pid_params_angle = {
    .kp = 20.0f,       // 较小的比例系数
    .ki = 1.0f,        // 较小的积分系数
    .kd = 0.0f,
};

// 返回角度环 - 响应较快，用于快速转向
PidParams_t pid_params_angle_back = {
    .kp = 84.0f,       // 较大的比例系数 (140*0.6)
    .ki = 0.05f,       // 很小的积分系数
    .kd = 100.0f,      // 较大的微分系数
};
```

**循迹环参数** (位置式PID)
```c
PidParams_t pid_params_line = {
    .kp = 140.0f,      // 大比例系数，快速响应
    .ki = 0.0f,        // 无积分，避免超调
    .kd = 0.0f,
};
```

#### 控制算法实现

**PID_Task() - 主控制任务**
```c
void PID_Task(void)
{
  if(pid_running == false) return;  // 控制使能检查

  // 1. 速度环控制 (增量式PID)
  output_left = pid_calculate_incremental(&pid_speed_left, left_encoder.speed_cm_s);
  output_right = pid_calculate_incremental(&pid_speed_right, right_encoder.speed_cm_s);

  // 2. 外环控制选择
  if(pid_mode == 0)  // 角度环控制模式
  {
    // 根据前进/返回模式选择不同的角度环
    if(angle_mode == 0) // 前进模式
      angle_output = pid_calculate_positional(&pid_angle, yaw);
    else // 返回模式
      angle_output = pid_calculate_positional(&pid_angle_back, yaw);
    
    // 差速转弯控制
    output_left = output_left - angle_output;
    output_right = output_right + angle_output;
  }
  else   // 循迹环控制模式
  {
    // 循迹位置误差控制
    line_output = pid_calculate_positional(&pid_line, g_line_position_error);
    
    // 差速转弯控制
    output_left = output_left - line_output;
    output_right = output_right + line_output;
  }

  // 3. 输出限幅
  output_left = pid_constrain(output_left, -999.0f, 999.0f);
  output_right = pid_constrain(output_right, -999.0f, 999.0f);
  
  // 4. 电机控制输出
  Motor_Set_Speed(&left_motor, output_left);
  Motor_Set_Speed(&right_motor, output_right);
}
```

#### 控制模式说明

**双环控制结构**:
- **内环 (速度环)**: 使用增量式PID，控制电机转速，响应快，稳定性好
- **外环 (角度/循迹环)**: 使用位置式PID，控制运动方向，精度高

**差速转弯原理**:
```
左轮速度 = 基础速度 - 转向输出
右轮速度 = 基础速度 + 转向输出
```
- 转向输出为正：右转 (左轮减速，右轮加速)
- 转向输出为负：左转 (左轮加速，右轮减速)

### 2. 编码器应用模块 (encoder_app)

#### 功能概述
编码器应用模块负责处理左右轮编码器数据，为PID控制系统提供速度反馈信息。

#### 核心功能实现

**Encoder_Init() - 编码器初始化**
```c
void Encoder_Init(void)
{
  Encoder_Driver_Init(&left_encoder, &htim3, 1);   // 左编码器，反向
  Encoder_Driver_Init(&right_encoder, &htim4, 0);  // 右编码器，正向
}
```

**Encoder_Task() - 数据更新任务**
```c
void Encoder_Task(void)
{
  Encoder_Driver_Update(&left_encoder);   // 更新左轮数据
  Encoder_Driver_Update(&right_encoder);  // 更新右轮数据
  
  // 可选：串口输出调试信息
  // Uart_Printf(&huart1, "Left:%.2fcm/s  Right:%.2fcm/s\r\n", 
  //            left_encoder.speed_cm_s, right_encoder.speed_cm_s);
}
```

#### 数据接口
- **输出数据**: `left_encoder.speed_cm_s`, `right_encoder.speed_cm_s`
- **更新频率**: 5ms (由调度器控制)
- **数据精度**: 浮点型，单位cm/s

### 3. MPU6050姿态解算应用 (mpu6050_app)

#### 功能概述
MPU6050应用模块负责读取六轴传感器数据，通过DMP进行姿态解算，为角度环控制提供角度反馈。

#### 核心变量
```c
float pitch, roll, yaw;  // 俯仰角、横滚角、偏航角
unsigned char yaw_mode;  // 角度处理模式
```

#### 核心功能实现

**Mpu6050_Init() - 传感器初始化**
```c
void Mpu6050_Init(void)
{
  MPU_Init();              // MPU6050硬件初始化
  mpu_dmp_init();          // DMP数字运动处理器初始化
  mpu6050_start_tick = HAL_GetTick();  // 记录启动时间
}
```

**Mpu6050_Task() - 姿态数据更新**
```c
void Mpu6050_Task(void)
{
  mpu_dmp_get_data(&pitch, &roll, &yaw);  // 获取DMP解算数据
  
  // 角度数据处理 - 解决角度跳变问题
  if(yaw_mode == 0 && yaw > 20) 
    yaw = yaw > 0 ? -yaw : yaw;  // 大角度时进行角度映射
}
```

#### 角度数据说明
- **pitch**: 俯仰角，前倾为负，后仰为正
- **roll**: 横滚角，左倾为负，右倾为正  
- **yaw**: 偏航角，左转为正，右转为负
- **数据范围**: -180° ~ +180°
- **更新频率**: 5ms

### 4. 灰度传感器循迹应用 (gray_app)

#### 功能概述
灰度传感器应用模块处理8路灰度传感器数据，计算循迹位置误差，为循迹控制提供位置反馈。

#### 核心变量
```c
unsigned char Digtal;           // 8位灰度传感器数据
float g_line_position_error;    // 循迹位置误差值
```

#### 位置误差计算算法

**Gray_Task() - 循迹数据处理**
```c
void Gray_Task(void)
{
    // 1. 读取灰度传感器数据
    Digtal = ~IIC_Get_Digtal();  // 取反处理，1表示检测到黑线
    
    // 2. 归一化处理
    IIC_Anolog_Normalize(0x00);
  
    // 3. 位置误差计算
    switch (Digtal)
    {
        // 中心区域 (理想状态)
        case 0x18: g_line_position_error = 0;     break; // 传感器3,4
        case 0x10: g_line_position_error = 1.5;   break; // 传感器4 (轻微偏右)
        case 0x08: g_line_position_error = -1.5;  break; // 传感器3 (轻微偏左)

        // 左侧偏移 (需要右转修正)
        case 0x0C: g_line_position_error = -2.5;  break; // 传感器2,3
        case 0x04: g_line_position_error = -4.5;  break; // 传感器2
        case 0x06: g_line_position_error = -6.5;  break; // 传感器1,2
        case 0x02: g_line_position_error = -8.5;  break; // 传感器1
        case 0x03: g_line_position_error = -10.5; break; // 传感器0,1
        case 0x01: g_line_position_error = -12.5; break; // 传感器0 (严重偏左)

        // 右侧偏移 (需要左转修正)
        case 0x30: g_line_position_error = 2.5;   break; // 传感器4,5
        case 0x20: g_line_position_error = 4.5;   break; // 传感器5
        case 0x60: g_line_position_error = 6.5;   break; // 传感器5,6
        case 0x40: g_line_position_error = 8.5;   break; // 传感器6
        case 0xC0: g_line_position_error = 10.5;  break; // 传感器6,7
        case 0x80: g_line_position_error = 12.5;  break; // 传感器7 (严重偏右)
        
        default:
            g_line_position_error = 0;  // 未知状态，保持直行
            break;
    }
}
```

#### 传感器布局与误差映射
```
传感器编号: 0  1  2  3  4  5  6  7
位置误差:  -12.5 -8.5 -4.5 -1.5 1.5 4.5 8.5 12.5
控制策略:  右转←←←←  直行  →→→→左转
```

### 5. OLED显示应用模块 (oled_app)

#### 功能概述
OLED显示应用模块负责系统状态的可视化显示，包括传感器数据、控制参数、系统模式等信息。

#### 显示内容设计

**Oled_Task() - 显示更新任务**
```c
void Oled_Task(void)
{
    // 第1行：灰度传感器状态 + 系统模式
    Oled_Printf(0, 0, 12, 0, "%d-%d-%d-%d-%d-%d-%d-%d",
               (Digtal>>0)&0x01, (Digtal>>1)&0x01, (Digtal>>2)&0x01, (Digtal>>3)&0x01,
               (Digtal>>4)&0x01, (Digtal>>5)&0x01, (Digtal>>6)&0x01, (Digtal>>7)&0x01);
    Oled_Printf(120, 0, 12, 0, "%d", system_mode);
    
    // 第2行：圈数计数
    Oled_Printf(114, 2, 12, 0, "%02d", gray_ff_count);
    
    // 第3-4行：编码器速度
    Oled_Printf(0, 2, 12, 0, "Left:%.2fcm/s  ", left_encoder.speed_cm_s);
    Oled_Printf(0, 4, 12, 0, "Right:%.2fcm/s  ", right_encoder.speed_cm_s);
    
    // 第6行：偏航角
    Oled_Printf(0, 6, 12, 0, "yaw:%f  ", yaw);
}
```

#### 显示布局
```
行0: [1-0-0-1-1-0-0-1]     [模式:2]
行1: 
行2: Left:15.23cm/s       [圈数:03]
行3: 
行4: Right:14.87cm/s
行5:
行6: yaw:-2.345
行7:
```

### 6. 按键交互应用模块 (key_app)

#### 功能概述
按键交互应用模块处理用户输入，实现系统模式切换、PID控制启停等交互功能。

#### 按键事件处理

**my_handle_key_event() - 按键事件回调**
```c
void my_handle_key_event(struct ebtn_btn *btn, ebtn_evt_t evt) {
    uint16_t click_cnt = ebtn_click_get_count(btn);
    
    switch (evt) {
        case EBTN_EVT_ONCLICK: // 单击/多击事件
            switch (click_cnt) 
            {
              case 1: // 单击 - 启动PID控制
                switch(system_mode) 
                {
                  case 1:
                  case 2:
                    pid_set_target(&pid_speed_left, 50);   // 设置目标速度50cm/s
                    pid_set_target(&pid_speed_right, 50);
                    break;
                  case 3:
                  case 4:
                    pid_set_target(&pid_speed_left, 45);   // 设置目标速度45cm/s
                    pid_set_target(&pid_speed_right, 45);
                    break;
                }
                pid_running = 1;  // 启动PID控制
                break;
            }
            break;
            
        case EBTN_EVT_KEEPALIVE: // 长按事件 - 模式切换
            if(++system_mode == 5) 
              system_mode = 1;  // 循环切换模式1-4
            break;
    }
}
```

#### 系统模式定义
- **模式1**: 直线行驶 A → B
- **模式2**: 绕行一圈 A → B → C → D  
- **模式3**: 8字绕行一圈 A → C → B → D
- **模式4**: 8字绕行多圈 A → C → B → D (循环)

### 7. LED状态指示应用 (led_app)

#### 功能概述
LED应用模块提供简单的状态指示功能，通过LED的亮灭状态反映系统运行状态。

#### 核心实现
```c
uint8_t led_state = 0;  // LED状态变量

void Led_Task()
{
    Led_Display(led_state);  // 根据状态变量控制LED
}
```

#### 状态指示逻辑
- **led_state = 1**: LED点亮，表示检测到特殊事件
- **led_state = 0**: LED熄灭，正常运行状态
- **闪烁模式**: 在定时器中断中实现1秒自动熄灭

### 8. 电机控制应用 (motor_app)

#### 功能概述
电机应用模块负责电机硬件的初始化配置，为PID控制提供电机实例。

#### 核心实现
```c
MOTOR left_motor;   // 左电机实例
MOTOR right_motor;  // 右电机实例

void Motor_Init(void)
{
    // 左电机配置：TIM1_CH1, AIN1/AIN2, 不反向, 死区45
    Motor_Config_Init(&left_motor, &htim1, TIM_CHANNEL_1, 
                      AIN1_GPIO_Port, AIN1_Pin, AIN2_GPIO_Port, AIN2_Pin, 0, 45);
    
    // 右电机配置：TIM1_CH2, BIN1/BIN2, 不反向, 死区45  
    Motor_Config_Init(&right_motor, &htim1, TIM_CHANNEL_2, 
                      BIN1_GPIO_Port, BIN1_Pin, BIN2_GPIO_Port, BIN2_Pin, 0, 45);
}
```

### 9. 串口通信应用 (uart_app)

#### 功能概述
串口应用模块处理与PC的通信，实现数据接收、处理和调试信息输出。

#### 核心功能实现

**Uart_Init() - 串口初始化**
```c
void Uart_Init(void)
{
  rt_ringbuffer_init(&ring_buffer, ring_buffer_input, BUFFER_SIZE);  // 初始化环形缓冲区
  HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, BUFFER_SIZE);  // 启动DMA接收
  __HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);  // 禁用半传输中断
}
```

**Uart_Task() - 数据处理任务**
```c
void Uart_Task(void)
{
  uint16_t uart_data_len = rt_ringbuffer_data_len(&ring_buffer);  // 检查缓冲区数据长度
  if(uart_data_len > 0)
  {
    rt_ringbuffer_get(&ring_buffer, uart_data_buffer, uart_data_len);  // 读取数据
    uart_data_buffer[uart_data_len] = '\0';  // 添加字符串结束符
    
    // 数据处理和回显
    Uart_Printf(&huart1, "Ringbuffer:%s\r\n", uart_data_buffer);
    
    memset(uart_data_buffer, 0, uart_data_len);  // 清空缓冲区
  }
}
```

## 模块间通信机制

### 全局变量共享
App层模块间主要通过全局变量进行数据共享：

```c
// 传感器数据
extern Encoder left_encoder, right_encoder;     // 编码器数据
extern float pitch, roll, yaw;                  // 姿态角度
extern unsigned char Digtal;                    // 灰度传感器数据
extern float g_line_position_error;             // 循迹误差

// 控制器实例
extern MOTOR left_motor, right_motor;           // 电机实例
extern PID_T pid_speed_left, pid_speed_right;   // 速度环PID
extern PID_T pid_angle, pid_line;               // 角度环和循迹环PID

// 系统状态
extern bool pid_running;                        // PID使能状态
extern bool pid_mode;                           // 控制模式
extern unsigned char system_mode;               // 系统模式
extern uint8_t led_state;                       // LED状态
```

### 数据流向图
```
编码器应用 → speed_cm_s → PID应用 → output → 电机应用
MPU6050应用 → yaw角度 → PID应用 → 角度控制 → 差速转弯
灰度应用 → position_error → PID应用 → 循迹控制 → 路径跟踪
按键应用 → system_mode → 系统状态 → 模式切换 → 参数调整
所有数据 → OLED应用 → 状态显示 → 用户反馈
调试数据 → 串口应用 → PC通信 → 数据监控
```

## 应用层设计模式

### 1. 任务驱动模式
每个应用模块都有对应的Task函数，由调度器周期性调用：
```c
void ModuleName_Task(void);  // 标准任务函数接口
```

### 2. 初始化分离模式
初始化和运行逻辑分离，便于系统启动管理：
```c
void ModuleName_Init(void);  // 初始化函数
void ModuleName_Task(void);  // 运行时任务函数
```

### 3. 数据共享模式
通过extern声明共享全局变量，实现模块间数据传递：
```c
extern DataType global_variable;  // 全局数据共享
```

### 4. 回调函数模式
使用回调函数处理异步事件，如按键事件：
```c
void my_handle_key_event(struct ebtn_btn *btn, ebtn_evt_t evt);
```

## 最佳实践指导

### 1. 模块设计原则
- **单一职责**: 每个模块专注于特定功能
- **低耦合**: 减少模块间直接依赖
- **高内聚**: 相关功能集中在同一模块
- **接口统一**: 遵循统一的命名和接口规范

### 2. 数据处理原则
- **实时性**: 保证控制系统的实时响应
- **稳定性**: 添加数据滤波和异常处理
- **精度**: 使用浮点数保证计算精度
- **效率**: 避免复杂计算影响系统性能

### 3. 控制算法原则
- **参数调试**: 从小到大逐步调整PID参数
- **稳定优先**: 优先保证系统稳定性
- **分层控制**: 采用多环控制提高性能
- **限幅保护**: 添加输出限幅保护硬件

### 4. 调试技巧
- **串口输出**: 使用串口输出关键数据
- **OLED显示**: 实时显示系统状态
- **LED指示**: 使用LED指示系统状态
- **分步调试**: 逐个模块进行功能验证

## 系统状态机设计

### 主状态机架构
```
系统启动 → 初始化完成 → 等待用户输入 → 运行控制 → 任务完成 → 等待输入
    ↓           ↓            ↓           ↓          ↓         ↑
硬件初始化   传感器校准    按键检测    PID控制    目标达成   模式切换
```

### 控制模式状态转换
```c
typedef enum {
    SYSTEM_MODE_IDLE = 0,      // 空闲模式
    SYSTEM_MODE_LINE_AB = 1,   // 直线A→B
    SYSTEM_MODE_CIRCLE = 2,    // 绕圈A→B→C→D
    SYSTEM_MODE_EIGHT_ONCE = 3,// 8字一圈A→C→B→D
    SYSTEM_MODE_EIGHT_LOOP = 4 // 8字循环
} SystemMode_t;
```

### PID控制状态机
```
PID停止 → PID启动 → 速度环稳定 → 外环控制 → 目标达成 → PID停止
   ↓         ↓          ↓           ↓          ↓         ↑
参数设置   使能控制   内环收敛    角度/循迹   误差收敛   任务完成
```

## 性能优化策略

### 1. 计算优化
```c
// 避免重复计算
static float last_speed_left = 0;
if(left_encoder.speed_cm_s != last_speed_left) {
    // 只在速度变化时计算PID
    output_left = pid_calculate_incremental(&pid_speed_left, left_encoder.speed_cm_s);
    last_speed_left = left_encoder.speed_cm_s;
}

// 使用查表法替代复杂计算
const float sin_table[360] = {0, 0.0175, 0.0349, ...}; // 预计算正弦表
```

### 2. 内存优化
```c
// 使用联合体节省内存
typedef union {
    struct {
        float kp, ki, kd;
        float error, last_error, integral;
    } pid_data;
    float raw_data[6];  // 便于批量操作
} PidUnion_t;

// 使用位域节省标志位内存
typedef struct {
    uint8_t pid_running : 1;    // PID运行标志
    uint8_t pid_mode : 1;       // PID模式
    uint8_t angle_mode : 1;     // 角度模式
    uint8_t yaw_mode : 1;       // 偏航模式
    uint8_t reserved : 4;       // 保留位
} SystemFlags_t;
```

### 3. 实时性优化
```c
// 中断优先级配置
void NVIC_Configuration(void)
{
    // 编码器中断 - 最高优先级
    HAL_NVIC_SetPriority(TIM3_IRQn, 0, 0);
    HAL_NVIC_SetPriority(TIM4_IRQn, 0, 0);

    // 系统定时器 - 中等优先级
    HAL_NVIC_SetPriority(TIM2_IRQn, 1, 0);

    // 串口中断 - 较低优先级
    HAL_NVIC_SetPriority(USART1_IRQn, 2, 0);
}

// 关键代码段保护
void Critical_PID_Calculate(void)
{
    __disable_irq();  // 禁用中断
    // 执行关键PID计算
    output_left = pid_calculate_incremental(&pid_speed_left, left_encoder.speed_cm_s);
    output_right = pid_calculate_incremental(&pid_speed_right, right_encoder.speed_cm_s);
    __enable_irq();   // 恢复中断
}
```

## 错误处理与异常保护

### 1. 传感器数据异常检测
```c
bool Sensor_Data_Check(void)
{
    // 编码器数据合理性检查
    if(fabs(left_encoder.speed_cm_s) > 200.0f || fabs(right_encoder.speed_cm_s) > 200.0f) {
        return false;  // 速度异常
    }

    // MPU6050数据合理性检查
    if(fabs(yaw) > 180.0f || isnan(yaw) || isinf(yaw)) {
        return false;  // 角度异常
    }

    // 灰度传感器数据检查
    if(Digtal == 0x00 || Digtal == 0xFF) {
        return false;  // 传感器异常
    }

    return true;  // 数据正常
}
```

### 2. PID输出保护
```c
void PID_Output_Protection(void)
{
    // 输出限幅保护
    output_left = pid_constrain(output_left, -999.0f, 999.0f);
    output_right = pid_constrain(output_right, -999.0f, 999.0f);

    // 输出变化率限制
    static float last_output_left = 0, last_output_right = 0;
    float max_change = 100.0f;  // 最大变化量

    if(fabs(output_left - last_output_left) > max_change) {
        output_left = last_output_left + (output_left > last_output_left ? max_change : -max_change);
    }
    if(fabs(output_right - last_output_right) > max_change) {
        output_right = last_output_right + (output_right > last_output_right ? max_change : -max_change);
    }

    last_output_left = output_left;
    last_output_right = output_right;
}
```

### 3. 系统安全保护
```c
void System_Safety_Check(void)
{
    static uint32_t error_count = 0;

    // 传感器数据检查
    if(!Sensor_Data_Check()) {
        error_count++;
        if(error_count > 10) {  // 连续10次错误
            // 紧急停车
            pid_running = false;
            Motor_Stop(&left_motor);
            Motor_Stop(&right_motor);
            led_state = 1;  // 点亮LED指示错误
        }
    } else {
        error_count = 0;  // 重置错误计数
    }
}
```

## 调试与测试方法

### 1. 分层调试策略
```c
// 第一层：硬件驱动测试
void Hardware_Test(void)
{
    // 测试电机驱动
    Motor_Set_Speed(&left_motor, 100);
    HAL_Delay(1000);
    Motor_Set_Speed(&left_motor, 0);

    // 测试编码器读取
    Uart_Printf(&huart1, "Encoder: L=%.2f, R=%.2f\r\n",
               left_encoder.speed_cm_s, right_encoder.speed_cm_s);

    // 测试传感器读取
    Uart_Printf(&huart1, "MPU6050: yaw=%.2f\r\n", yaw);
    Uart_Printf(&huart1, "Gray: 0x%02X, Error=%.2f\r\n", Digtal, g_line_position_error);
}

// 第二层：单环PID测试
void Single_Loop_Test(void)
{
    // 仅测试速度环
    pid_mode = 0;  // 禁用外环
    pid_set_target(&pid_speed_left, 50);
    pid_set_target(&pid_speed_right, 50);
    pid_running = true;
}

// 第三层：双环控制测试
void Dual_Loop_Test(void)
{
    // 测试角度环+速度环
    pid_mode = 0;  // 角度环模式
    pid_set_target(&pid_angle, 0);  // 目标角度0度
    pid_running = true;
}
```

### 2. 数据记录与分析
```c
// 数据记录结构
typedef struct {
    uint32_t timestamp;
    float target_speed_left, target_speed_right;
    float actual_speed_left, actual_speed_right;
    float pid_output_left, pid_output_right;
    float yaw_angle, line_error;
} DataLog_t;

DataLog_t data_log[1000];  // 数据记录缓冲区
uint16_t log_index = 0;

void Data_Logger(void)
{
    if(log_index < 1000) {
        data_log[log_index].timestamp = HAL_GetTick();
        data_log[log_index].target_speed_left = pid_speed_left.target;
        data_log[log_index].actual_speed_left = left_encoder.speed_cm_s;
        data_log[log_index].pid_output_left = output_left;
        // ... 记录其他数据
        log_index++;
    }
}
```

### 3. 实时监控界面
```c
void Debug_Monitor(void)
{
    static uint32_t last_print_time = 0;

    if(HAL_GetTick() - last_print_time > 100) {  // 100ms输出一次
        // 输出CSV格式数据，便于Excel分析
        Uart_Printf(&huart1, "%lu,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f\r\n",
                   HAL_GetTick(),
                   pid_speed_left.target, left_encoder.speed_cm_s,
                   pid_speed_right.target, right_encoder.speed_cm_s,
                   yaw, g_line_position_error);

        last_print_time = HAL_GetTick();
    }
}
```

## 扩展开发指南

### 1. 添加新传感器模块
```c
// 1. 创建传感器应用文件
// sensor_new_app.c / sensor_new_app.h

// 2. 定义数据结构
typedef struct {
    float value1, value2;
    uint8_t status;
} NewSensor_t;

extern NewSensor_t new_sensor;

// 3. 实现标准接口
void NewSensor_Init(void);
void NewSensor_Task(void);

// 4. 在调度器中添加任务
// Scheduler.c中添加：
// {NewSensor_Task, 0, 10, 0},  // 10ms周期
```

### 2. 扩展控制算法
```c
// 1. 定义新的控制器
typedef struct {
    float kp, ki, kd;
    float target, current, output;
    // 添加新算法特有参数
} NewController_t;

// 2. 实现控制算法
float new_control_calculate(NewController_t *ctrl, float input) {
    // 实现新的控制算法
    return output;
}

// 3. 集成到主控制循环
void PID_Task(void) {
    // 原有PID控制
    // ...

    // 新控制算法
    if(new_control_enabled) {
        new_output = new_control_calculate(&new_controller, sensor_input);
        // 融合控制输出
        final_output = pid_output * 0.7f + new_output * 0.3f;
    }
}
```

### 3. 添加通信协议
```c
// 1. 定义协议格式
typedef struct {
    uint8_t header;     // 帧头
    uint8_t cmd;        // 命令字
    uint8_t length;     // 数据长度
    uint8_t data[32];   // 数据
    uint8_t checksum;   // 校验和
} Protocol_t;

// 2. 实现协议解析
void Protocol_Parse(uint8_t *buffer, uint16_t length) {
    Protocol_t *frame = (Protocol_t*)buffer;

    switch(frame->cmd) {
        case CMD_SET_SPEED:
            // 设置速度命令
            break;
        case CMD_GET_STATUS:
            // 获取状态命令
            break;
    }
}

// 3. 集成到串口应用
void Uart_Task(void) {
    // 原有数据处理
    // ...

    // 协议解析
    Protocol_Parse(uart_data_buffer, uart_data_len);
}
```

---

*本文档详细介绍了STM32F4工程中App层各应用模块的业务逻辑和实现方法，包括系统状态机设计、性能优化策略、错误处理机制、调试测试方法和扩展开发指南，为嵌入式应用开发提供了完整的设计参考和最佳实践指导。*

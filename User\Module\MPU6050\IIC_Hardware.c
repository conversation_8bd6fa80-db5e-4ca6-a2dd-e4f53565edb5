#include "stm32f4xx_hal.h"
#include "IIC.h"
#include "i2c.h"
#include "mpu6050.h"

/* 使用硬件I2C1替代软件模拟I2C */
extern I2C_HandleTypeDef hi2c1;

/*
*********************************************************************************************************
*	函 数 名: IIC_GPIO_Init
*	功能说明: 硬件I2C初始化（由CubeMX自动生成，此处为空实现）
*	形    参: 无
*	返 回 值: 无
*********************************************************************************************************
*/
void IIC_GPIO_Init(void)
{
    // 硬件I2C初始化由CubeMX自动完成，此处无需操作
}

/*
*********************************************************************************************************
*	函 数 名: IIC_Start
*	功能说明: 硬件I2C开始信号（HAL库自动处理）
*	形    参: 无
*	返 回 值: 无
*********************************************************************************************************
*/
void IIC_Start(void)
{
    // 硬件I2C开始信号由HAL库自动处理
}

/*
*********************************************************************************************************
*	函 数 名: IIC_Stop
*	功能说明: 硬件I2C停止信号（HAL库自动处理）
*	形    参: 无
*	返 回 值: 无
*********************************************************************************************************
*/
void IIC_Stop(void)
{
    // 硬件I2C停止信号由HAL库自动处理
}

/*
*********************************************************************************************************
*	函 数 名: IIC_Send_Byte
*	功能说明: 硬件I2C发送字节
*	形    参: _ucByte 待发送的字节
*	返 回 值: 无
*********************************************************************************************************
*/
void IIC_Send_Byte(uint8_t _ucByte)
{
    // 此函数在硬件I2C中由HAL_I2C_Master_Transmit处理
}

/*
*********************************************************************************************************
*	函 数 名: IIC_Read_Byte
*	功能说明: 硬件I2C读取字节
*	形    参: ack 应答信号
*	返 回 值: 读取的数据
*********************************************************************************************************
*/
uint8_t IIC_Read_Byte(uint8_t ack)
{
    // 此函数在硬件I2C中由HAL_I2C_Master_Receive处理
    return 0;
}

/*
*********************************************************************************************************
*	函 数 名: IIC_Wait_Ack
*	功能说明: 硬件I2C等待应答（HAL库自动处理）
*	形    参: 无
*	返 回 值: 0表示正确应答，1表示无器件响应
*********************************************************************************************************
*/
uint8_t IIC_Wait_Ack(void)
{
    // 硬件I2C应答由HAL库自动处理
    return 0;
}

/*
*********************************************************************************************************
*	函 数 名: IIC_Ack
*	功能说明: 硬件I2C发送应答（HAL库自动处理）
*	形    参: 无
*	返 回 值: 无
*********************************************************************************************************
*/
void IIC_Ack(void)
{
    // 硬件I2C应答由HAL库自动处理
}

/*
*********************************************************************************************************
*	函 数 名: IIC_NAck
*	功能说明: 硬件I2C发送非应答（HAL库自动处理）
*	形    参: 无
*	返 回 值: 无
*********************************************************************************************************
*/
void IIC_NAck(void)
{
    // 硬件I2C非应答由HAL库自动处理
}

/*
*********************************************************************************************************
*	函 数 名: IIC_CheckDevice
*	功能说明: 检测I2C总线设备，使用硬件I2C
*	形    参: _Address设备的I2C总线地址
*	返 回 值: 返回值 0 表示正确， 返回1表示未探测到
*********************************************************************************************************
*/
uint8_t IIC_CheckDevice(uint8_t _Address)
{
    HAL_StatusTypeDef result;
    
    /* 使用HAL库检测设备 */
    result = HAL_I2C_IsDeviceReady(&hi2c1, _Address, 3, 1000);
    
    if(result == HAL_OK)
        return 0;  // 设备存在
    else
        return 1;  // 设备不存在
}

/*
*********************************************************************************************************
*	函 数 名: MPU_Write_Len
*	功能说明: IIC连续写数据，使用硬件I2C
*	形    参: addr:器件地址, reg:寄存器地址, len:写数据长度, buf:数据区
*	返 回 值: 0,正常; 其他,错误代码
*********************************************************************************************************
*/
uint8_t MPU_Write_Len(uint8_t addr, uint8_t reg, uint8_t len, uint8_t *buf)
{
    HAL_StatusTypeDef result;
    
    result = HAL_I2C_Mem_Write(&hi2c1, addr, reg, I2C_MEMADD_SIZE_8BIT, buf, len, 1000);
    
    if(result == HAL_OK)
        return 0;
    else
        return 1;
}

/*
*********************************************************************************************************
*	函 数 名: MPU_Read_Len
*	功能说明: IIC连续读数据，使用硬件I2C
*	形    参: addr:器件地址, reg:寄存器地址, len:读数据长度, buf:读数据缓冲区
*	返 回 值: 0,正常; 其他,错误代码
*********************************************************************************************************
*/
uint8_t MPU_Read_Len(uint8_t addr, uint8_t reg, uint8_t len, uint8_t *buf)
{
    HAL_StatusTypeDef result;
    
    result = HAL_I2C_Mem_Read(&hi2c1, addr, reg, I2C_MEMADD_SIZE_8BIT, buf, len, 1000);
    
    if(result == HAL_OK)
        return 0;
    else
        return 1;
}

/*
*********************************************************************************************************
*	函 数 名: MPU_Write_Byte
*	功能说明: IIC写一个字节，使用硬件I2C
*	形    参: reg:寄存器地址, data:数据
*	返 回 值: 0,正常; 其他,错误代码
*********************************************************************************************************
*/
uint8_t MPU_Write_Byte(uint8_t reg, uint8_t data)
{
    HAL_StatusTypeDef result;
    
    result = HAL_I2C_Mem_Write(&hi2c1, MPU_ADDR << 1, reg, I2C_MEMADD_SIZE_8BIT, &data, 1, 1000);
    
    if(result == HAL_OK)
        return 0;
    else
        return 1;
}

/*
*********************************************************************************************************
*	函 数 名: MPU_Read_Byte
*	功能说明: IIC读一个字节，使用硬件I2C
*	形    参: reg:寄存器地址
*	返 回 值: 读到的数据
*********************************************************************************************************
*/
uint8_t MPU_Read_Byte(uint8_t reg)
{
    uint8_t data;
    HAL_StatusTypeDef result;
    
    result = HAL_I2C_Mem_Read(&hi2c1, MPU_ADDR << 1, reg, I2C_MEMADD_SIZE_8BIT, &data, 1, 1000);
    
    if(result == HAL_OK)
        return data;
    else
        return 0xFF;  // 读取失败返回0xFF
}

#include "oled_app.h"

extern Encoder left_encoder;
extern Encoder right_encoder;

extern MOTOR left_motor;
extern MOTOR right_motor;

extern PID_T pid_speed_left;  // �����ٶȻ� PID ������
extern PID_T pid_speed_right; // �����ٶȻ� PID ������

extern PID_T pid_angle; // ǰ���ǶȻ�

extern PID_T pid_line; // ѭ��λ��ʽ PID ������(ѭ����)

extern float pitch, roll, yaw; // pitch-�����ǣ���-��+����roll����ǣ�ǰ-��+����yaw-����ǣ���+��-����Ҫ���������ǵ�xyz�ῴ

extern unsigned char Digtal; // �Ҷȴ�����������
extern float g_line_position_error; // ѭ�����ֵ��ѭ������ǰֵ��

extern unsigned char system_mode; // ϵͳģʽ��1~4 ��Ӧ�ĵ���Ŀ

extern unsigned char gray_ff_count; // ��Ȧ����Ȧ������

void Oled_Init(void)
{
  // 添加延时确保I2C2初始化完成
  HAL_Delay(100);

  // 测试I2C2通信
  HAL_StatusTypeDef result = HAL_I2C_IsDeviceReady(&hi2c2, 0x78, 3, 1000);
  if(result != HAL_OK) {
    // I2C通信失败，可能需要检查硬件连接
    // 这里可以添加错误处理代码
  }

  OLED_Init();
  HAL_Delay(50);  // 初始化后延时

  // 强制开启显示
  OLED_Display_On();
  HAL_Delay(10);

  OLED_Clear();

  // 先测试基本显示功能 - 显示简单图案
  OLED_Set_Pos(0, 0);
  for(int i = 0; i < 16; i++) {
    OLED_WR_DATA(0xFF);  // 显示实心块
  }

  OLED_Set_Pos(0, 1);
  for(int i = 0; i < 16; i++) {
    OLED_WR_DATA(0xAA);  // 显示条纹图案
  }

  // 然后测试字符显示
  OLED_ShowChar(0, 3, 'A', 12, 1);
  OLED_ShowChar(8, 3, 'B', 12, 1);
}

void Oled_Task(void)
{

    Oled_Printf(120, 0, 12, 0, "%d", system_mode);
    Oled_Printf(0, 0, 12, 0, "%d-%d-%d-%d-%d-%d-%d-%d",(Digtal>>0)&0x01,(Digtal>>1)&0x01,(Digtal>>2)&0x01,(Digtal>>3)&0x01,(Digtal>>4)&0x01,(Digtal>>5)&0x01,(Digtal>>6)&0x01,(Digtal>>7)&0x01);
    //Oled_Printf(0, 0, 12, 0, "Left:%d  Right:%d  ", left_motor.speed, right_motor.speed);
  
    Oled_Printf(114, 2, 12, 0, "%02d", gray_ff_count);
  
    Oled_Printf(0, 2, 12, 0, "Left:%.2fcm/s  ", left_encoder.speed_cm_s);
    Oled_Printf(0, 4, 12, 0, "Right:%.2fcm/s  ", right_encoder.speed_cm_s);
  
  
    Oled_Printf(0, 6, 12, 0, "yaw:%f  ", yaw);
  
//    Uart_Printf(&huart1, "%f,%f\r\n", pid_line_right.target, g_line_position_error); // ����ѭ����
  
//    Uart_Printf(&huart1, "%f,%f\r\n", pid_angle.target, yaw); // ���ԽǶȻ�
  
//    Uart_Printf(&huart1, "%f,%f,%f,%f\r\n", pid_speed_left.target, left_encoder.speed_cm_s, pid_speed_right.target, right_encoder.speed_cm_s); // �����ٶȻ�
}

# STM32F4 Driver层硬件驱动模块详解

## 概述

Driver层是整个系统的硬件抽象层，负责直接与STM32F4的外设进行交互，为上层应用提供统一、简洁的硬件操作接口。本层采用模块化设计，每个硬件设备都有对应的驱动模块，实现了硬件细节的封装和接口的标准化。

### 设计理念
- **硬件抽象**: 隐藏底层HAL库的复杂性，提供简洁的API
- **模块化设计**: 每个硬件设备独立封装，便于维护和扩展
- **统一接口**: 所有驱动遵循相同的命名规范和接口设计
- **配置灵活**: 支持运行时配置和参数调整

## Driver层架构

### 文件组织结构
```
User/Driver/
├── led_driver.c/.h          # LED指示灯驱动
├── key_driver.c/.h          # 按键输入驱动
├── motor_driver.c/.h        # TB6612电机驱动
├── encoder_driver.c/.h      # 编码器速度测量驱动
├── uart_driver.c/.h         # 串口通信驱动
└── oled_driver.c/.h         # OLED显示驱动
```

### 硬件连接图
```
STM32F407VGT6
├── GPIO
│   ├── PA0  → 按键输入 (Key)
│   ├── PB2  → LED指示灯
│   ├── PE8  → TB6612 BIN1 (右电机方向1)
│   ├── PE10 → TB6612 BIN2 (右电机方向2)
│   ├── PE12 → TB6612 AIN1 (左电机方向1)
│   └── PE14 → TB6612 AIN2 (左电机方向2)
├── PWM (TIM1/TIM3)
│   ├── TIM1_CH1 → 左电机PWM控制
│   └── TIM3_CH1 → 右电机PWM控制
├── Encoder (TIM3/TIM4)
│   ├── TIM3 → 左编码器 (A/B相)
│   └── TIM4 → 右编码器 (A/B相)
├── I2C1 → OLED显示屏 (SSD1306)
└── UART1 → 串口通信 (调试/数据传输)
```

## 核心驱动模块详解

### 1. LED驱动模块 (led_driver)

#### 功能概述
LED驱动模块提供简单的LED状态控制功能，支持状态缓存和重复写入优化。

#### 核心数据结构
```c
// LED连接定义 (main.h)
#define LED_Pin GPIO_PIN_2
#define LED_GPIO_Port GPIOB
```

#### 核心函数详解

**Led_Display() - LED状态控制**
```c
void Led_Display(uint8_t enable)
{
  static uint8_t led_temp_old = 0x00;  // 状态缓存
  
  if(enable != led_temp_old)  // 避免重复写入
  {
    HAL_GPIO_WritePin(LED_GPIO_Port, LED_Pin, 
                      enable ? GPIO_PIN_SET : GPIO_PIN_RESET);
    led_temp_old = enable;  // 更新状态缓存
  }
}
```

**设计特点**:
- **状态缓存**: 避免重复的GPIO操作，提高效率
- **简洁接口**: 只需传入0/1即可控制LED开关
- **低功耗**: 减少不必要的GPIO切换

#### 使用示例
```c
Led_Display(1);  // 点亮LED
Led_Display(0);  // 熄灭LED
```

### 2. 按键驱动模块 (key_driver)

#### 功能概述
按键驱动模块集成了高级按键处理库(ebtn)，支持防抖、多击、长按等复杂按键事件检测。

#### 硬件连接
```c
#define Key_Pin GPIO_PIN_0
#define Key_GPIO_Port GPIOA
```

#### 核心函数详解

**Key_Read() - 基础按键读取**
```c
unsigned char Key_Read()
{
    unsigned char key_temp = 0;
    
    if(HAL_GPIO_ReadPin(Key_GPIO_Port, Key_Pin) == GPIO_PIN_SET)
        key_temp = 1;  // 高电平表示按下

    return key_temp;
}
```

**my_get_key_state() - ebtn库状态回调**
```c
uint8_t my_get_key_state(struct ebtn_btn *btn) {
    switch (btn->key_id) {
        case 1: // 读取KEY1的状态
            return (HAL_GPIO_ReadPin(Key_GPIO_Port, Key_Pin) == GPIO_PIN_SET);
        default:
            return 0;  // 未知按键ID
    }
}
```

#### 按键参数配置
```c
static const ebtn_btn_param_t defaul_ebtn_param = EBTN_PARAMS_INIT(
    20,     // time_debounce: 按下稳定 20ms
    20,     // time_debounce_release: 释放稳定 20ms
    50,     // time_click_pressed_min: 单击最短时间 50ms
    500,    // time_click_pressed_max: 单击最长时间 500ms
    200,    // time_click_multi_max: 多次单击间隔 200ms
    1000,   // time_keepalive_period: 长按事件周期 1000ms
    5       // max_consecutive: 最多支持 5 连击
);
```

#### 使用示例
```c
// 在App层的按键事件处理函数中
void my_handle_key_event(ebtn_btn_t *btn, ebtn_event_t event) {
    switch(event) {
        case EBTN_EVENT_CLICK_SINGLE:
            // 单击事件处理
            break;
        case EBTN_EVENT_CLICK_DOUBLE:
            // 双击事件处理
            break;
        case EBTN_EVENT_KEEPALIVE:
            // 长按事件处理
            break;
    }
}
```

### 3. 电机驱动模块 (motor_driver)

#### 功能概述
电机驱动模块基于TB6612芯片，提供双电机的PWM速度控制、方向控制、死区补偿等功能。

#### 核心数据结构

**MOTOR_Config - 电机配置结构体**
```c
typedef struct MOTOR_Config{
    TIM_HandleTypeDef *htim;     // 定时器句柄
    uint32_t pwm_channel;        // PWM通道
    struct {
        GPIO_TypeDef *port;      // GPIO端口
        uint16_t pin;            // GPIO引脚
    } in1, in2;                  // 对应TB6612的AIN/BIN
    unsigned char reverse;       // 电机方向是否反转
} MOTOR_Config;
```

**MOTOR - 电机实例结构体**
```c
typedef struct MOTOR
{
    MOTOR_Config config;         // 配置信息
    int speed;                   // 当前速度
    int dead_band_speed;         // 死区补偿速度
} MOTOR;
```

#### TB6612控制原理

TB6612是双路电机驱动芯片，每路电机需要3个控制信号：
- **PWM**: 控制电机转速 (占空比0-100%)
- **IN1/IN2**: 控制电机转向

**转向控制逻辑**:
| IN1 | IN2 | 电机状态 |
|-----|-----|---------|
| 0   | 0   | 停止    |
| 0   | 1   | 正转    |
| 1   | 0   | 反转    |
| 1   | 1   | 刹车    |

#### 核心函数详解

**Motor_Config_Init() - 电机初始化**
```c
void Motor_Config_Init(MOTOR* motor, TIM_HandleTypeDef *htim, uint32_t pwm_channel, 
                       GPIO_TypeDef *in1_port, uint16_t in1_pin, 
                       GPIO_TypeDef *in2_port, uint16_t in2_pin, 
                       unsigned char reverse, int dead_band_speed)
{
    // 配置参数保存
    motor->config.htim = htim;
    motor->config.pwm_channel = pwm_channel;
    motor->config.in1.port = in1_port;
    motor->config.in1.pin = in1_pin;
    motor->config.in2.port = in2_port;
    motor->config.in2.pin = in2_pin;
    motor->config.reverse = reverse;
    
    motor->dead_band_speed = dead_band_speed;
    motor->speed = 0;
  
    // 初始化GPIO状态 (正转方向)
    HAL_GPIO_WritePin(motor->config.in1.port, motor->config.in1.pin, GPIO_PIN_SET);
    HAL_GPIO_WritePin(motor->config.in2.port, motor->config.in2.pin, GPIO_PIN_RESET);

    // 启动PWM输出
    HAL_TIM_PWM_Start(motor->config.htim, motor->config.pwm_channel);
    __HAL_TIM_SET_COMPARE(motor->config.htim, motor->config.pwm_channel, 0);
}
```

**Motor_Dead_Compensation() - 死区补偿**
```c
int Motor_Dead_Compensation(MOTOR* motor)
{
  if(motor->speed > 0 && motor->speed < motor->dead_band_speed)
    return motor->dead_band_speed;      // 正向死区补偿
  else if(motor->speed < 0 && motor->speed > -motor->dead_band_speed) 
    return -motor->dead_band_speed;     // 反向死区补偿
  else
    return motor->speed;                // 无需补偿
}
```

**Motor_Set_Speed() - 速度控制**
```c
void Motor_Set_Speed(MOTOR* motor, int speed)
{
    // 1. 速度限幅
    motor->speed = Motor_Limit_Speed(motor, speed, 
                                   motor->config.htim->Init.Period, 
                                   -(motor->config.htim->Init.Period));
  
    // 2. 死区补偿
    motor->speed = Motor_Dead_Compensation(motor);

    // 3. 方向控制
    if(motor->speed >= 0) // 正转
    {
        HAL_GPIO_WritePin(motor->config.in1.port, motor->config.in1.pin, 
                         motor->config.reverse == 0 ? GPIO_PIN_SET : GPIO_PIN_RESET);
        HAL_GPIO_WritePin(motor->config.in2.port, motor->config.in2.pin, 
                         motor->config.reverse == 0 ? GPIO_PIN_RESET : GPIO_PIN_SET);
    }
    else // 反转
    {
        HAL_GPIO_WritePin(motor->config.in1.port, motor->config.in1.pin, 
                         motor->config.reverse == 0 ? GPIO_PIN_RESET : GPIO_PIN_SET);
        HAL_GPIO_WritePin(motor->config.in2.port, motor->config.in2.pin, 
                         motor->config.reverse == 0 ? GPIO_PIN_SET : GPIO_PIN_RESET);
    }

    // 4. PWM占空比设置
    __HAL_TIM_SET_COMPARE(motor->config.htim, motor->config.pwm_channel, 
                         Motor_ABS(motor->speed));
}
```

#### 使用示例
```c
MOTOR left_motor, right_motor;

// 初始化左电机
Motor_Config_Init(&left_motor, &htim1, TIM_CHANNEL_1, 
                  AIN1_GPIO_Port, AIN1_Pin, AIN2_GPIO_Port, AIN2_Pin, 
                  0, 30);  // 不反向，死区补偿30

// 控制电机
Motor_Set_Speed(&left_motor, 500);   // 正转，速度500
Motor_Set_Speed(&left_motor, -300);  // 反转，速度300
Motor_Stop(&left_motor);             // 停止
Motor_Brake(&left_motor);            // 刹车
```

### 4. 编码器驱动模块 (encoder_driver)

#### 功能概述
编码器驱动模块利用STM32的编码器模式，实现高精度的速度测量和位置反馈。

#### 硬件参数配置
```c
// 编码器参数 (根据实际硬件修改)
#define ENCODER_PPR (13 * 20 * 4)    // 13线/相, 20倍减速比, 4倍频 = 1040PPR
#define WHEEL_DIAMETER_CM 4.8f       // 车轮直径 4.8cm
#define WHEEL_CIRCUMFERENCE_CM (WHEEL_DIAMETER_CM * PI)  // 周长计算
#define SAMPLING_TIME_S 0.005f       // 采样时间 5ms
```

#### 核心数据结构
```c
typedef struct
{
  TIM_HandleTypeDef *htim;    // 定时器句柄
  unsigned char reverse;      // 方向是否反转
  int16_t count;             // 当前周期计数值
  int32_t total_count;       // 累计总计数
  float speed_cm_s;          // 计算速度 (cm/s)
} Encoder;
```

#### 速度计算原理

**速度计算公式**:
```
速度(cm/s) = (计数值 / PPR) × 周长(cm) / 采样时间(s)
```

**参数说明**:
- **计数值**: 采样周期内的编码器脉冲数
- **PPR**: 每转脉冲数 (Pulse Per Revolution)
- **周长**: 车轮周长，由直径计算得出
- **采样时间**: 数据更新周期

#### 核心函数详解

**Encoder_Driver_Init() - 编码器初始化**
```c
void Encoder_Driver_Init(Encoder* encoder, TIM_HandleTypeDef *htim, unsigned char reverse)
{
  encoder->htim = htim;
  encoder->reverse = reverse;
  
  // 启动定时器编码器模式
  HAL_TIM_Encoder_Start(encoder->htim, TIM_CHANNEL_ALL);

  // 清零计数器
  __HAL_TIM_SetCounter(encoder->htim, 0);

  // 初始化数据
  encoder->count = 0;
  encoder->total_count = 0;
  encoder->speed_cm_s = 0.0f;
}
```

**Encoder_Driver_Update() - 数据更新**
```c
void Encoder_Driver_Update(Encoder* encoder)
{
  // 1. 读取硬件计数值
  encoder->count = (int16_t)__HAL_TIM_GetCounter(encoder->htim);
  
  // 2. 处理方向反转
  encoder->count = encoder->reverse == 0 ? encoder->count : -encoder->count;

  // 3. 清零计数器，准备下个周期
  __HAL_TIM_SetCounter(encoder->htim, 0);

  // 4. 累计总数
  encoder->total_count += encoder->count;

  // 5. 计算速度
  encoder->speed_cm_s = (float)encoder->count / ENCODER_PPR * 
                        WHEEL_CIRCUMFERENCE_CM / SAMPLING_TIME_S;
}
```

#### 使用示例
```c
Encoder left_encoder, right_encoder;

// 初始化编码器
Encoder_Driver_Init(&left_encoder, &htim3, 0);   // 左编码器，不反向
Encoder_Driver_Init(&right_encoder, &htim4, 1);  // 右编码器，反向

// 在5ms周期任务中更新数据
void Encoder_Task(void) {
    Encoder_Driver_Update(&left_encoder);
    Encoder_Driver_Update(&right_encoder);
    
    // 获取速度数据
    float left_speed = left_encoder.speed_cm_s;
    float right_speed = right_encoder.speed_cm_s;
}
```

### 5. 串口驱动模块 (uart_driver)

#### 功能概述
串口驱动模块基于DMA和环形缓冲区，实现高效的串口通信，支持printf格式化输出和中断接收。

#### 核心数据结构
```c
#define BUFFER_SIZE 128

uint8_t uart_rx_dma_buffer[BUFFER_SIZE];    // DMA接收缓冲区
uint8_t ring_buffer_input[BUFFER_SIZE];     // 环形缓冲区存储
struct rt_ringbuffer ring_buffer;          // 环形缓冲区管理
uint8_t uart_data_buffer[BUFFER_SIZE];      // 数据处理缓冲区
```

#### 核心函数详解

**Uart_Printf() - 格式化输出**
```c
int Uart_Printf(UART_HandleTypeDef *huart, const char *format, ...)
{
    char buffer[512];     // 临时缓冲区
    va_list arg;          // 可变参数列表
    int len;              // 字符串长度

    va_start(arg, format);
    // 格式化字符串到buffer
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);

    // 通过HAL库发送数据
    HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF);
    return len;
}
```

**HAL_UARTEx_RxEventCallback() - DMA接收回调**
```c
void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
    if (huart->Instance == USART1)
    {
        // 1. 停止当前DMA传输
        HAL_UART_DMAStop(huart);

        // 2. 将接收到的数据放入环形缓冲区
        rt_ringbuffer_put(&ring_buffer, uart_rx_dma_buffer, Size);
        
        // 3. 清空DMA缓冲区
        memset(uart_rx_dma_buffer, 0, sizeof(uart_rx_dma_buffer));

        // 4. 重新启动DMA接收
        HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, 
                                     sizeof(uart_rx_dma_buffer));
        
        // 5. 禁用半传输中断
        __HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);
    }
}
```

#### 使用示例
```c
// 发送格式化数据
Uart_Printf(&huart1, "Speed: L=%.2f, R=%.2f cm/s\r\n", 
            left_speed, right_speed);

// 在应用层处理接收数据
void Uart_Task(void) {
    uint16_t len = rt_ringbuffer_data_len(&ring_buffer);
    if(len > 0) {
        rt_ringbuffer_get(&ring_buffer, uart_data_buffer, len);
        // 处理接收到的数据
    }
}
```

### 6. OLED驱动模块 (oled_driver)

#### 功能概述
OLED驱动模块基于I2C通信，为SSD1306控制器的OLED显示屏提供高级显示接口。

#### 核心函数详解

**Oled_Printf() - 格式化显示**
```c
int Oled_Printf(uint8_t x, uint8_t y, uint8_t Char_Size, 
                uint8_t Color_Turn, const char *format, ...)
{
    char buffer[128];     // 显示缓冲区
    va_list arg;          // 可变参数
    int len;              // 字符串长度

    va_start(arg, format);
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);

    // 调用底层显示函数
    OLED_ShowString(x, y, buffer, Char_Size, Color_Turn);

    return len;
}
```

**OLED_SendBuff() - 缓冲区批量传输**
```c
void OLED_SendBuff(uint8_t buff[8][128])  
{
    for(uint8_t page=0; page<8; page++) {
        OLED_Set_Pos(0, page);  // 设置页地址
        
        // 使用I2C批量传输一页数据
        HAL_I2C_Mem_Write(&hi2c1, 
                        0x78,          // OLED地址
                        0x40,          // 数据寄存器
                        I2C_MEMADD_SIZE_8BIT,
                        buff[page],    // 页数据
                        128,           // 128字节
                        100);          // 超时100ms
    }
}
```

#### 使用示例
```c
// 显示格式化文本
Oled_Printf(0, 0, 12, 0, "Speed: %.1f", speed);
Oled_Printf(0, 2, 12, 0, "Mode: %d", mode);

// 批量更新显示缓冲区
uint8_t display_buffer[8][128];
// ... 填充显示数据 ...
OLED_SendBuff(display_buffer);
```

## 驱动接口设计规范

### 命名规范
- **初始化函数**: `ModuleName_Init()` 或 `ModuleName_Config_Init()`
- **数据更新函数**: `ModuleName_Update()`
- **控制函数**: `ModuleName_Set_XXX()` 或 `ModuleName_XXX()`
- **状态读取函数**: `ModuleName_Get_XXX()` 或 `ModuleName_Read()`

### 参数传递规范
- **结构体指针**: 传递设备实例的指针
- **配置参数**: 通过初始化函数传入
- **返回值**: 成功返回0，失败返回负数

### 错误处理规范
- **参数检查**: 在函数入口检查参数有效性
- **状态检查**: 检查硬件状态和初始化状态
- **错误返回**: 统一的错误码定义

## 性能优化技巧

### 1. GPIO操作优化
- **状态缓存**: 避免重复的GPIO写入操作
- **批量操作**: 使用寄存器直接操作多个GPIO
- **中断优化**: 合理设置中断优先级

### 2. DMA优化
- **缓冲区设计**: 使用双缓冲或环形缓冲
- **传输长度**: 优化DMA传输的数据长度
- **中断处理**: 快速处理DMA完成中断

### 3. 定时器优化
- **预分频设置**: 合理设置定时器预分频值
- **计数器清零**: 及时清零避免溢出
- **中断频率**: 控制中断频率避免系统负载过高

## 常见问题与解决方案

### 1. 电机抖动问题
**原因**: 死区补偿不当或PWM频率过低
**解决方案**: 
- 调整死区补偿参数
- 提高PWM频率到20kHz以上
- 检查电源纹波

### 2. 编码器计数异常
**原因**: 信号干扰或接线问题
**解决方案**:
- 检查编码器供电和接地
- 添加滤波电容
- 使用屏蔽线缆

### 3. 串口数据丢失
**原因**: 缓冲区溢出或DMA配置错误
**解决方案**:
- 增大缓冲区大小
- 及时处理接收数据
- 检查DMA配置

### 4. OLED显示异常
**原因**: I2C通信错误或初始化不当
**解决方案**:
- 检查I2C时钟和数据线
- 确认OLED地址正确
- 添加I2C错误处理

---

*本文档详细介绍了STM32F4工程中Driver层各硬件驱动模块的设计原理和使用方法，为嵌入式硬件驱动开发提供了完整的参考资料。*

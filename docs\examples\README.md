# STM32F4 智能小车综合使用示例

## 概述

本文档提供STM32F4智能小车系统的完整使用示例，从基础的硬件连接到复杂的功能实现，帮助初学者快速上手并掌握整个系统的使用方法。

### 示例分类
- **基础示例**: 单个模块的基本使用
- **进阶示例**: 多模块协同工作
- **完整项目**: 实际应用场景的完整实现
- **调试示例**: 问题诊断和性能优化

## 基础使用示例

### 示例1: LED控制基础
```c
/**
 * @brief LED基础控制示例
 * @note 演示LED的基本开关控制
 */
#include "MyDefine.h"

void LED_Basic_Example(void)
{
    // 1. 初始化LED
    Led_Init();
    
    // 2. 基本控制
    Led_Display(1);     // 点亮LED
    HAL_Delay(1000);    // 延时1秒
    Led_Display(0);     // 熄灭LED
    HAL_Delay(1000);    // 延时1秒
    
    // 3. 闪烁控制
    for(int i = 0; i < 10; i++) {
        Led_Display(i % 2); // 交替闪烁
        HAL_Delay(200);     // 200ms间隔
    }
}
```

### 示例2: 电机基础控制
```c
/**
 * @brief 电机基础控制示例
 * @note 演示电机的基本运动控制
 */
void Motor_Basic_Example(void)
{
    // 1. 初始化电机
    Motor_Init();
    
    // 2. 基本运动控制
    printf("=== Motor Basic Control ===\n");
    
    // 前进
    printf("Forward...\n");
    Motor_Set_Speed(&left_motor, 300);
    Motor_Set_Speed(&right_motor, 300);
    HAL_Delay(2000);
    
    // 停止
    printf("Stop...\n");
    Motor_Set_Speed(&left_motor, 0);
    Motor_Set_Speed(&right_motor, 0);
    HAL_Delay(1000);
    
    // 后退
    printf("Backward...\n");
    Motor_Set_Speed(&left_motor, -300);
    Motor_Set_Speed(&right_motor, -300);
    HAL_Delay(2000);
    
    // 左转
    printf("Turn Left...\n");
    Motor_Set_Speed(&left_motor, -200);
    Motor_Set_Speed(&right_motor, 200);
    HAL_Delay(1000);
    
    // 右转
    printf("Turn Right...\n");
    Motor_Set_Speed(&left_motor, 200);
    Motor_Set_Speed(&right_motor, -200);
    HAL_Delay(1000);
    
    // 最终停止
    Motor_Set_Speed(&left_motor, 0);
    Motor_Set_Speed(&right_motor, 0);
    printf("Motor test complete.\n");
}
```

### 示例3: 编码器速度测量
```c
/**
 * @brief 编码器速度测量示例
 * @note 演示编码器的速度测量功能
 */
void Encoder_Speed_Example(void)
{
    // 1. 初始化编码器
    Encoder_Init();
    
    printf("=== Encoder Speed Measurement ===\n");
    
    // 2. 启动电机并测量速度
    Motor_Set_Speed(&left_motor, 500);
    Motor_Set_Speed(&right_motor, 500);
    
    // 3. 连续测量10秒
    uint32_t start_time = HAL_GetTick();
    while(HAL_GetTick() - start_time < 10000) {
        // 更新编码器数据
        Encoder_Task();
        
        // 输出速度信息
        printf("Left: %d counts, %.2f cm/s | Right: %d counts, %.2f cm/s\n",
               left_encoder.count, left_encoder.speed_cm_s,
               right_encoder.count, right_encoder.speed_cm_s);
        
        HAL_Delay(500); // 每500ms输出一次
    }
    
    // 4. 停止电机
    Motor_Set_Speed(&left_motor, 0);
    Motor_Set_Speed(&right_motor, 0);
    printf("Speed measurement complete.\n");
}
```

### 示例4: OLED显示基础
```c
/**
 * @brief OLED显示基础示例
 * @note 演示OLED的基本显示功能
 */
void OLED_Display_Example(void)
{
    // 1. 初始化OLED
    Oled_Init();
    
    // 2. 清屏
    OLED_Clear();
    
    // 3. 显示文本
    OLED_ShowString(0, 0, "STM32F4 Car", 16);
    OLED_ShowString(0, 16, "System Ready", 16);
    OLED_ShowString(0, 32, "Version: 1.0", 16);
    OLED_ShowString(0, 48, "2024-01-01", 16);
    
    // 4. 显示数字
    int counter = 0;
    for(int i = 0; i < 20; i++) {
        OLED_ShowString(80, 32, "    ", 16); // 清除旧数字
        OLED_ShowNum(80, 32, counter++, 3, 16);
        HAL_Delay(500);
    }
    
    // 5. 显示系统状态
    OLED_Clear();
    OLED_ShowString(0, 0, "System Status:", 16);
    OLED_ShowString(0, 16, "Motor: OK", 16);
    OLED_ShowString(0, 32, "Sensor: OK", 16);
    OLED_ShowString(0, 48, "Ready to run", 16);
}
```

## 进阶使用示例

### 示例5: PID速度控制
```c
/**
 * @brief PID速度控制示例
 * @note 演示PID控制器的速度控制功能
 */
void PID_Speed_Control_Example(void)
{
    printf("=== PID Speed Control Example ===\n");
    
    // 1. 初始化所有相关模块
    Motor_Init();
    Encoder_Init();
    PID_Init();
    
    // 2. 设置PID参数
    pid_speed_left.kp = 0.8f;
    pid_speed_left.ki = 0.1f;
    pid_speed_left.kd = 0.05f;
    
    pid_speed_right.kp = 0.8f;
    pid_speed_right.ki = 0.1f;
    pid_speed_right.kd = 0.05f;
    
    // 3. 启用PID控制
    PID_Set_Enable(true);
    
    // 4. 设置目标速度并运行
    printf("Setting target speed: 20 cm/s\n");
    PID_Set_Target_Speed(20.0f, 20.0f);
    
    // 5. 监控控制效果
    uint32_t start_time = HAL_GetTick();
    while(HAL_GetTick() - start_time < 15000) { // 运行15秒
        // 执行PID控制任务
        PID_Task();
        
        // 输出控制状态
        printf("Target: %.1f,%.1f | Actual: %.1f,%.1f | Output: %d,%d\n",
               pid_speed_left.target, pid_speed_right.target,
               left_encoder.speed_cm_s, right_encoder.speed_cm_s,
               left_motor.speed, right_motor.speed);
        
        HAL_Delay(200);
    }
    
    // 6. 停止控制
    PID_Set_Enable(false);
    Motor_Set_Speed(&left_motor, 0);
    Motor_Set_Speed(&right_motor, 0);
    printf("PID control test complete.\n");
}
```

### 示例6: 姿态传感器应用
```c
/**
 * @brief MPU6050姿态传感器应用示例
 * @note 演示姿态传感器的数据读取和处理
 */
void MPU6050_Attitude_Example(void)
{
    printf("=== MPU6050 Attitude Sensor Example ===\n");
    
    // 1. 初始化MPU6050
    Mpu6050_Init();
    
    // 2. 等待传感器稳定
    printf("Sensor stabilizing...\n");
    HAL_Delay(2000);
    
    // 3. 连续读取姿态数据
    printf("Reading attitude data (10 seconds):\n");
    printf("Time(s) | Pitch(°) | Roll(°) | Yaw(°)\n");
    printf("--------|----------|---------|--------\n");
    
    uint32_t start_time = HAL_GetTick();
    while(HAL_GetTick() - start_time < 10000) {
        // 更新传感器数据
        Mpu6050_Task();
        
        // 输出姿态角度
        float time_s = (HAL_GetTick() - start_time) / 1000.0f;
        printf("%6.1f  | %7.2f  | %6.2f  | %6.2f\n", 
               time_s, pitch, roll, yaw);
        
        HAL_Delay(500);
    }
    
    printf("Attitude measurement complete.\n");
}
```

### 示例7: 灰度传感器循迹
```c
/**
 * @brief 灰度传感器循迹示例
 * @note 演示灰度传感器的循迹功能
 */
void Gray_Line_Following_Example(void)
{
    printf("=== Gray Sensor Line Following Example ===\n");
    
    // 1. 初始化相关模块
    Gray_Init();
    Motor_Init();
    PID_Init();
    
    // 2. 设置循迹模式
    pid_mode = 1; // 循迹模式
    PID_Set_Enable(true);
    
    // 3. 校准传感器
    printf("Calibrating sensors...\n");
    printf("Place robot on white surface, then press any key...\n");
    // 等待按键输入
    while(!Key_Get_State()) {
        HAL_Delay(10);
    }
    
    // 执行白色校准
    Gray_Calibrate_White();
    printf("White calibration done.\n");
    
    printf("Place robot on black line, then press any key...\n");
    while(!Key_Get_State()) {
        HAL_Delay(10);
    }
    
    // 执行黑线校准
    Gray_Calibrate_Black();
    printf("Black calibration done.\n");
    
    // 4. 开始循迹
    printf("Starting line following...\n");
    PID_Set_Target_Speed(15.0f, 15.0f); // 设置基础速度
    
    uint32_t start_time = HAL_GetTick();
    while(HAL_GetTick() - start_time < 30000) { // 运行30秒
        // 执行循迹任务
        Gray_Task();
        PID_Task();
        
        // 输出传感器状态
        printf("Sensors: %02X | Position: %.2f | Speed: %.1f,%.1f\n",
               Digtal, g_line_position_error,
               left_encoder.speed_cm_s, right_encoder.speed_cm_s);
        
        HAL_Delay(100);
    }
    
    // 5. 停止循迹
    PID_Set_Enable(false);
    Motor_Set_Speed(&left_motor, 0);
    Motor_Set_Speed(&right_motor, 0);
    printf("Line following test complete.\n");
}
```

## 完整项目示例

### 示例8: 智能避障小车
```c
/**
 * @brief 智能避障小车完整示例
 * @note 综合使用多个模块实现避障功能
 */
void Smart_Obstacle_Avoidance_Car(void)
{
    printf("=== Smart Obstacle Avoidance Car ===\n");
    
    // 1. 系统初始化
    System_Init();
    
    // 2. 显示启动信息
    OLED_Clear();
    OLED_ShowString(0, 0, "Smart Car v1.0", 16);
    OLED_ShowString(0, 16, "Obstacle Avoid", 16);
    OLED_ShowString(0, 32, "Initializing...", 16);
    HAL_Delay(2000);
    
    // 3. 设置控制参数
    PID_Set_Enable(true);
    PID_Set_Target_Speed(20.0f, 20.0f);
    
    // 4. 主控制循环
    uint32_t last_display_time = 0;
    bool obstacle_detected = false;
    
    printf("Starting obstacle avoidance mode...\n");
    
    while(1) {
        uint32_t current_time = HAL_GetTick();
        
        // 检测障碍物 (使用姿态传感器模拟)
        Mpu6050_Task();
        
        // 简单的障碍物检测逻辑 (基于俯仰角变化)
        if(fabs(pitch) > 15.0f) { // 俯仰角超过15度认为有障碍
            obstacle_detected = true;
        } else {
            obstacle_detected = false;
        }
        
        // 避障控制逻辑
        if(obstacle_detected) {
            // 检测到障碍物，执行避障动作
            printf("Obstacle detected! Avoiding...\n");
            
            // 停止前进
            PID_Set_Enable(false);
            Motor_Set_Speed(&left_motor, 0);
            Motor_Set_Speed(&right_motor, 0);
            HAL_Delay(500);
            
            // 后退
            Motor_Set_Speed(&left_motor, -300);
            Motor_Set_Speed(&right_motor, -300);
            HAL_Delay(1000);
            
            // 右转
            Motor_Set_Speed(&left_motor, 300);
            Motor_Set_Speed(&right_motor, -300);
            HAL_Delay(800);
            
            // 继续前进
            PID_Set_Enable(true);
            PID_Set_Target_Speed(20.0f, 20.0f);
            
        } else {
            // 正常前进
            PID_Task();
        }
        
        // 更新显示 (每500ms更新一次)
        if(current_time - last_display_time >= 500) {
            OLED_Clear();
            OLED_ShowString(0, 0, "Smart Car", 16);
            
            if(obstacle_detected) {
                OLED_ShowString(0, 16, "Status: Avoiding", 16);
            } else {
                OLED_ShowString(0, 32, "Status: Moving", 16);
            }
            
            // 显示速度
            char speed_str[20];
            sprintf(speed_str, "L:%.1f R:%.1f", 
                   left_encoder.speed_cm_s, right_encoder.speed_cm_s);
            OLED_ShowString(0, 48, speed_str, 16);
            
            last_display_time = current_time;
        }
        
        // 检查按键退出
        if(Key_Get_State()) {
            printf("User requested stop.\n");
            break;
        }
        
        HAL_Delay(50); // 主循环延时
    }
    
    // 5. 停止所有运动
    PID_Set_Enable(false);
    Motor_Set_Speed(&left_motor, 0);
    Motor_Set_Speed(&right_motor, 0);
    
    OLED_Clear();
    OLED_ShowString(0, 0, "System Stopped", 16);
    printf("Obstacle avoidance demo complete.\n");
}
```

## 调试示例

### 示例9: 系统性能监控
```c
/**
 * @brief 系统性能监控示例
 * @note 演示系统性能监控和调试技巧
 */
void System_Performance_Monitor(void)
{
    printf("=== System Performance Monitor ===\n");
    
    // 1. 初始化系统
    System_Init();
    
    // 2. 性能监控变量
    uint32_t loop_count = 0;
    uint32_t max_loop_time = 0;
    uint32_t min_loop_time = 0xFFFFFFFF;
    uint32_t total_loop_time = 0;
    
    // 3. 监控循环
    uint32_t monitor_start = HAL_GetTick();
    
    while(HAL_GetTick() - monitor_start < 10000) { // 监控10秒
        uint32_t loop_start = HAL_GetTick();
        
        // 执行系统任务
        LED_Task();
        Motor_Task();
        Encoder_Task();
        PID_Task();
        Mpu6050_Task();
        Gray_Task();
        Key_Task();
        Oled_Task();
        Uart_Task();
        
        uint32_t loop_end = HAL_GetTick();
        uint32_t loop_time = loop_end - loop_start;
        
        // 统计性能数据
        loop_count++;
        total_loop_time += loop_time;
        
        if(loop_time > max_loop_time) {
            max_loop_time = loop_time;
        }
        
        if(loop_time < min_loop_time) {
            min_loop_time = loop_time;
        }
        
        // 每1000次循环输出一次统计
        if(loop_count % 1000 == 0) {
            float avg_time = (float)total_loop_time / loop_count;
            printf("Loops: %lu | Avg: %.2fms | Min: %lums | Max: %lums\n",
                   loop_count, avg_time, min_loop_time, max_loop_time);
        }
        
        HAL_Delay(1); // 最小延时
    }
    
    // 4. 输出最终统计
    float avg_time = (float)total_loop_time / loop_count;
    float loops_per_sec = loop_count / 10.0f;
    
    printf("\n=== Performance Summary ===\n");
    printf("Total loops: %lu\n", loop_count);
    printf("Average loop time: %.2f ms\n", avg_time);
    printf("Min loop time: %lu ms\n", min_loop_time);
    printf("Max loop time: %lu ms\n", max_loop_time);
    printf("Loops per second: %.1f\n", loops_per_sec);
    printf("System efficiency: %.1f%%\n", (1.0f / avg_time) * 100.0f);
}
```

### 示例10: 错误诊断和恢复
```c
/**
 * @brief 错误诊断和恢复示例
 * @note 演示系统错误检测和自动恢复机制
 */
void Error_Diagnosis_Recovery_Example(void)
{
    printf("=== Error Diagnosis and Recovery Example ===\n");
    
    // 1. 初始化系统
    System_Init();
    
    // 2. 错误计数器
    uint32_t motor_errors = 0;
    uint32_t sensor_errors = 0;
    uint32_t communication_errors = 0;
    
    // 3. 诊断循环
    for(int test_cycle = 0; test_cycle < 100; test_cycle++) {
        printf("\n--- Test Cycle %d ---\n", test_cycle + 1);
        
        // 电机诊断
        printf("Testing motors...\n");
        Motor_Set_Speed(&left_motor, 500);
        Motor_Set_Speed(&right_motor, 500);
        HAL_Delay(100);
        
        // 检查编码器反馈
        Encoder_Task();
        if(fabs(left_encoder.speed_cm_s) < 1.0f || fabs(right_encoder.speed_cm_s) < 1.0f) {
            motor_errors++;
            printf("ERROR: Motor response insufficient!\n");
            
            // 尝试恢复
            Motor_Set_Speed(&left_motor, 0);
            Motor_Set_Speed(&right_motor, 0);
            HAL_Delay(500);
            
            // 重新初始化
            Motor_Init();
            Encoder_Init();
            printf("Motor system reset.\n");
        } else {
            printf("Motors: OK\n");
        }
        
        Motor_Set_Speed(&left_motor, 0);
        Motor_Set_Speed(&right_motor, 0);
        
        // 传感器诊断
        printf("Testing sensors...\n");
        Mpu6050_Task();
        
        // 检查传感器数据合理性
        if(fabs(pitch) > 90.0f || fabs(roll) > 90.0f || fabs(yaw) > 180.0f) {
            sensor_errors++;
            printf("ERROR: Sensor data out of range!\n");
            
            // 尝试恢复
            Mpu6050_Init();
            HAL_Delay(1000);
            printf("Sensor system reset.\n");
        } else {
            printf("Sensors: OK (Pitch:%.1f, Roll:%.1f, Yaw:%.1f)\n", 
                   pitch, roll, yaw);
        }
        
        // 通信诊断
        printf("Testing communication...\n");
        char test_msg[] = "Communication test\r\n";
        if(Uart_Send_String(&huart1, test_msg) != HAL_OK) {
            communication_errors++;
            printf("ERROR: UART communication failed!\n");
            
            // 尝试恢复
            Uart_Init();
            printf("UART system reset.\n");
        } else {
            printf("Communication: OK\n");
        }
        
        // 显示错误统计
        printf("Error Summary - Motor:%lu, Sensor:%lu, Comm:%lu\n",
               motor_errors, sensor_errors, communication_errors);
        
        HAL_Delay(1000);
    }
    
    // 4. 最终报告
    printf("\n=== Final Diagnosis Report ===\n");
    printf("Total test cycles: 100\n");
    printf("Motor errors: %lu (%.1f%%)\n", motor_errors, motor_errors / 100.0f * 100);
    printf("Sensor errors: %lu (%.1f%%)\n", sensor_errors, sensor_errors / 100.0f * 100);
    printf("Communication errors: %lu (%.1f%%)\n", communication_errors, communication_errors / 100.0f * 100);
    
    float system_reliability = (300 - motor_errors - sensor_errors - communication_errors) / 300.0f * 100;
    printf("System reliability: %.1f%%\n", system_reliability);
    
    if(system_reliability > 95.0f) {
        printf("System status: EXCELLENT\n");
    } else if(system_reliability > 85.0f) {
        printf("System status: GOOD\n");
    } else if(system_reliability > 70.0f) {
        printf("System status: FAIR - Needs attention\n");
    } else {
        printf("System status: POOR - Requires maintenance\n");
    }
}
```

---

*本示例文档提供了从基础到高级的完整使用示例，涵盖了STM32F4智能小车系统的各个方面。通过这些示例，初学者可以逐步掌握系统的使用方法，并能够开发出自己的应用程序。*

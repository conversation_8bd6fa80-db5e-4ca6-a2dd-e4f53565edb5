# STM32F4 嵌入式工程架构总览

## 工程简介

本工程是一个基于STM32F407VGT6微控制器的智能小车控制系统，采用HAL库开发，实现了PID控制算法的小车运动控制。工程设计面向嵌入式开发初学者，采用模块化、分层化的架构设计，便于理解和扩展。

### 工程背景
- **目标平台**: STM32F407VGT6 (ARM Cortex-M4)
- **开发环境**: Keil MDK-ARM + STM32CubeMX
- **应用场景**: 智能小车、机器人控制、嵌入式学习
- **设计目标**: 教学友好、模块化、易扩展

## 技术栈介绍

### 核心技术栈
- **微控制器**: STM32F407VGT6 (168MHz, 1MB Flash, 192KB RAM)
- **开发框架**: STM32 HAL库 (Hardware Abstraction Layer)
- **开发工具**: 
  - STM32CubeMX: 图形化配置工具
  - Keil MDK-ARM: 集成开发环境
  - STM32 ST-LINK: 调试下载工具

### 外设配置
- **定时器**: TIM1(PWM输出), TIM2(系统定时), TIM3/TIM4(编码器)
- **通信接口**: UART1(调试通信), I2C1/I2C3(传感器通信)
- **DMA**: USART1_RX DMA传输
- **GPIO**: 电机控制、LED指示、按键输入

## 工程目录结构

```
08_PID/                          # 工程根目录
├── 08_PID.ioc                   # STM32CubeMX配置文件
├── Core/                        # STM32 HAL核心文件
│   ├── Inc/                     # 核心头文件
│   │   ├── main.h              # 主程序头文件
│   │   ├── stm32f4xx_hal_conf.h # HAL库配置
│   │   ├── gpio.h              # GPIO配置
│   │   ├── tim.h               # 定时器配置
│   │   ├── usart.h             # 串口配置
│   │   └── i2c.h               # I2C配置
│   └── Src/                     # 核心源文件
│       ├── main.c              # 主程序入口
│       ├── stm32f4xx_hal_msp.c # HAL MSP配置
│       ├── stm32f4xx_it.c      # 中断服务程序
│       ├── gpio.c              # GPIO初始化
│       ├── tim.c               # 定时器初始化
│       ├── usart.c             # 串口初始化
│       └── i2c.c               # I2C初始化
├── Drivers/                     # STM32 HAL驱动库
│   ├── STM32F4xx_HAL_Driver/   # HAL驱动源码
│   └── CMSIS/                  # CMSIS标准接口
├── MDK-ARM/                     # Keil工程文件
│   ├── 08_PID.uvprojx          # Keil工程配置
│   └── startup_stm32f407xx.s   # 启动文件
└── User/                        # 用户代码目录
    ├── MyDefine.h              # 统一头文件管理
    ├── Scheduler.c/.h          # 任务调度器
    ├── Scheduler_Task.c/.h     # 系统任务管理
    ├── Driver/                 # 硬件驱动层
    │   ├── led_driver.c/.h     # LED驱动
    │   ├── motor_driver.c/.h   # 电机驱动(TB6612)
    │   ├── encoder_driver.c/.h # 编码器驱动
    │   ├── uart_driver.c/.h    # 串口驱动
    │   └── oled_driver.c/.h    # OLED驱动
    ├── App/                    # 应用逻辑层
    │   ├── led_app.c/.h        # LED应用
    │   ├── motor_app.c/.h      # 电机应用
    │   ├── encoder_app.c/.h    # 编码器应用
    │   ├── pid_app.c/.h        # PID控制应用
    │   ├── mpu6050_app.c/.h    # MPU6050应用
    │   ├── oled_app.c/.h       # OLED显示应用
    │   ├── uart_app.c/.h       # 串口通信应用
    │   ├── key_app.c/.h        # 按键应用
    │   └── gray_app.c/.h       # 灰度传感器应用
    └── Module/                 # 第三方模块
        ├── PID/                # PID算法库
        ├── MPU6050/            # MPU6050传感器库
        ├── 0.96 Oled/          # OLED显示库
        ├── Ebtn/               # 按键处理库
        ├── Grayscale/          # 灰度传感器库
        ├── Ringbuffer/         # 环形缓冲区库
        └── WouoUI-Page/        # UI界面库
```

## 三层架构设计

本工程采用经典的三层架构设计模式，实现了良好的模块化和分层解耦：

### 1. Driver层 (硬件抽象层)
**职责**: 封装具体硬件操作，提供统一的硬件接口
**特点**:
- 直接操作STM32 HAL库API
- 屏蔽硬件细节，提供简洁接口
- 便于硬件移植和更换

**主要模块**:
- `led_driver`: LED控制驱动
- `motor_driver`: TB6612电机驱动芯片控制
- `encoder_driver`: 编码器速度测量
- `uart_driver`: 串口通信驱动
- `oled_driver`: OLED显示驱动

### 2. App层 (应用逻辑层)
**职责**: 实现具体业务逻辑，调用Driver层接口
**特点**:
- 不直接操作硬件，通过Driver层接口
- 实现具体的应用功能和业务逻辑
- 模块间可以相互调用和协作

**主要模块**:
- `pid_app`: PID控制算法应用
- `motor_app`: 电机控制应用
- `encoder_app`: 编码器数据处理
- `mpu6050_app`: 姿态传感器应用
- `oled_app`: 显示界面应用

### 3. Scheduler层 (任务调度层)
**职责**: 管理系统任务调度和时间管理
**特点**:
- 基于时间片的协作式调度
- 支持不同周期的任务执行
- 统一的任务管理接口

## 模块化设计理念

### 统一接口设计
所有模块都遵循统一的接口设计模式：
- `XXX_Init()`: 模块初始化接口
- `XXX_Task()`: 周期性任务接口  
- `XXX_Config()`: 配置接口
- `XXX_Driver_XXX()`: 驱动操作接口

### 依赖管理
通过`MyDefine.h`统一管理所有模块的头文件依赖：
```c
// HAL库依赖
#include "main.h"
#include "gpio.h"
#include "tim.h"
// ... 其他HAL头文件

// 用户模块依赖
#include "led_driver.h"
#include "motor_driver.h"
// ... 其他用户模块
```

### 数据流向
```
传感器数据 → Driver层 → App层 → 控制算法 → Driver层 → 执行器
     ↑                                                    ↓
     └─────────────── Scheduler调度管理 ←──────────────────┘
```

## 分层解耦的优势

### 1. 可维护性
- 每层职责明确，修改影响范围可控
- 模块独立，便于单独测试和调试
- 代码结构清晰，易于理解和维护

### 2. 可扩展性  
- 新增功能只需在对应层添加模块
- 硬件更换只需修改Driver层
- 算法优化只需修改App层

### 3. 可移植性
- Driver层封装硬件差异
- App层业务逻辑与硬件无关
- 便于移植到其他STM32平台

### 4. 团队协作
- 不同层可以并行开发
- 接口明确，减少沟通成本
- 便于代码复用和共享

## 适用场景

### 学习场景
- 嵌入式系统入门学习
- STM32开发技能培养
- 软件架构设计理解
- PID控制算法学习

### 应用场景
- 智能小车控制系统
- 简单机器人控制
- 电机控制应用
- 传感器数据采集

### 扩展场景
- 添加更多传感器模块
- 实现更复杂的控制算法
- 集成无线通信功能
- 开发上位机监控系统

## 下一步学习建议

1. **理解调度系统**: 学习`Scheduler`模块的工作原理
2. **掌握驱动开发**: 深入学习各Driver模块的实现
3. **应用层开发**: 理解业务逻辑的实现方式
4. **PID控制**: 重点学习PID算法的应用
5. **系统集成**: 理解各模块间的协作关系

---

*本文档为STM32F4嵌入式工程架构总览，更多详细内容请参考各模块的专门文档。*

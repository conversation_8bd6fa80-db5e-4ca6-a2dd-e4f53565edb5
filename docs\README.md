# STM32F4 智能小车项目文档

## 项目概述

本项目是基于STM32F407VGT6微控制器的智能小车系统，采用三层架构设计，集成了多种传感器和执行器，实现了PID控制、循迹、避障等功能。

## 📚 完整文档导航

### 🚀 快速入门
- [**快速入门指南**](quick-start-guide.md) - 新手必读，从零开始搭建和运行项目
- [**使用示例大全**](examples/README.md) - 丰富的代码示例，从基础到高级应用
- [**常见问题解答**](faq/README.md) - 问题诊断和解决方案集合

### 🏗️ 系统架构
- [**项目架构概览**](architecture/README.md) - 系统整体架构和设计思想
- [**任务调度系统**](scheduler/README.md) - 协作式任务调度器详解

### 🔧 模块详解
- [**驱动层文档**](drivers/README.md) - 硬件驱动模块详解 (6个驱动模块)
- [**应用层文档**](applications/README.md) - 应用逻辑模块详解 (9个应用模块)
- [**第三方模块**](modules/README.md) - 第三方库集成详解 (7个第三方模块)

### 🎯 专项技术
- [**PID控制系统**](pid-control/README.md) - 双环级联PID控制算法详解

### 👨‍💻 开发指南
- [**开发规范与最佳实践**](development/README.md) - 编码规范、调试技巧、性能优化

### 📋 代码模板
- [**驱动层模板**](templates/driver_template.h) - 标准化驱动开发模板
- [**应用层模板**](templates/app_template.h) - 标准化应用开发模板

## 🎯 学习路径推荐

### 初学者路径 (🔰 新手推荐)
1. **[快速入门指南](quick-start-guide.md)** - 了解硬件连接和基础操作
2. **[项目架构概览](architecture/README.md)** - 理解系统整体设计
3. **[使用示例 - 基础部分](examples/README.md#基础使用示例)** - 学习单个模块使用
4. **[常见问题解答](faq/README.md)** - 解决遇到的问题

### 进阶开发路径 (⚡ 有基础)
1. **[驱动层文档](drivers/README.md)** - 深入理解硬件抽象
2. **[应用层文档](applications/README.md)** - 掌握业务逻辑实现
3. **[PID控制系统](pid-control/README.md)** - 学习控制算法
4. **[使用示例 - 进阶部分](examples/README.md#进阶使用示例)** - 多模块协同开发

### 高级优化路径 (🚀 深度定制)
1. **[任务调度系统](scheduler/README.md)** - 优化系统实时性
2. **[第三方模块](modules/README.md)** - 集成新的功能模块
3. **[开发规范](development/README.md)** - 提升代码质量
4. **[使用示例 - 完整项目](examples/README.md#完整项目示例)** - 构建复杂应用

## 🛠️ 技术特点

### 核心架构
- **三层架构**: Driver → App → Scheduler 清晰分层设计
- **模块化设计**: 高内聚低耦合，易于扩展和维护
- **统一接口**: 标准化的模块接口和编程规范
- **实时调度**: 基于时间片的协作式多任务调度

### 控制算法
- **双环级联PID**: 速度环 + 角度/循迹环的级联控制
- **参数自适应**: 支持在线参数调优和自动调节
- **多模式切换**: 速度控制、循迹、避障等多种控制模式
- **死区补偿**: 电机死区自动补偿算法

### 传感器融合
- **MPU6050**: 六轴姿态传感器，DMP硬件解算
- **编码器**: 高精度速度和位置测量 (1040 PPR)
- **灰度传感器**: 8路灰度传感器阵列，支持循迹
- **多传感器协同**: 传感器数据融合和互补滤波

### 通信与显示
- **UART通信**: DMA + 环形缓冲区高效通信
- **I2C总线**: 多设备I2C通信管理
- **OLED显示**: 实时状态显示和调试信息
- **调试接口**: 丰富的调试输出和参数调节接口

## 🔧 硬件平台

### 主要硬件
- **主控**: STM32F407VGT6 (ARM Cortex-M4, 168MHz, 1MB Flash, 192KB RAM)
- **电机**: 带编码器的直流减速电机 × 2 (1040 PPR编码器)
- **驱动**: TB6612电机驱动板 (支持双电机独立控制)
- **传感器**: MPU6050六轴传感器、8路灰度传感器阵列
- **显示**: 0.96寸OLED显示屏 (SSD1306, I2C接口)
- **接口**: UART、I2C、PWM、GPIO等丰富接口

### 性能指标
- **控制频率**: 200Hz (5ms控制周期)
- **速度精度**: ±0.1 cm/s
- **角度精度**: ±0.1°
- **响应时间**: <50ms
- **稳态误差**: <2%

## 📊 项目统计

### 代码规模
- **总代码行数**: ~8000行
- **驱动层**: 6个模块，~2000行
- **应用层**: 9个模块，~3000行
- **第三方模块**: 7个模块，~2500行
- **系统核心**: ~500行

### 文档规模
- **技术文档**: 8个主要文档
- **代码示例**: 10个完整示例
- **FAQ条目**: 14个常见问题
- **开发模板**: 2套标准模板
- **总文档量**: ~15000字

## 🤝 贡献指南

### 开发规范
- 遵循[开发规范文档](development/README.md)中的编码标准
- 使用提供的[代码模板](templates/)进行开发
- 添加充分的注释和文档说明
- 进行充分的测试验证

### 文档贡献
- 补充和完善现有文档
- 添加新的使用示例
- 更新FAQ和问题解答
- 提供翻译和本地化支持

## 📞 技术支持

### 问题反馈
1. 首先查阅[常见问题解答](faq/README.md)
2. 搜索相关[技术文档](README.md)
3. 查看[使用示例](examples/README.md)
4. 如仍无法解决，请提交Issue

### 学习资源
- **STM32官方文档**: HAL库参考手册
- **控制理论**: PID控制理论基础
- **嵌入式系统**: 实时系统设计原理
- **传感器技术**: 多传感器融合算法

---

*本项目为STM32F4智能小车的完整开源实现，包含详细的技术文档和丰富的示例代码。适合嵌入式系统学习、机器人控制研究和工程项目开发。*

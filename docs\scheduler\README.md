# STM32F4 Scheduler调度系统详解

## 概述

Scheduler调度系统是本工程的核心组件，负责管理和调度所有应用层任务的执行。它采用**协作式多任务调度**机制，基于STM32 HAL库的`HAL_GetTick()`函数实现时间片管理，为嵌入式系统提供了简单而有效的任务调度解决方案。

### 设计目标
- **简单易懂**: 适合嵌入式开发初学者理解和使用
- **资源高效**: 占用最少的系统资源
- **实时性好**: 保证关键任务的及时执行
- **易于扩展**: 方便添加新的任务模块

## 调度器架构

### 核心文件结构
```
User/
├── Scheduler.h          # 调度器接口声明
├── Scheduler.c          # 调度器核心实现
├── Scheduler_Task.h     # 系统任务管理接口
└── Scheduler_Task.c     # 系统初始化和任务实现
```

### 调度器组成
1. **任务结构体**: 定义任务的基本属性
2. **任务数组**: 存储所有需要调度的任务
3. **调度算法**: 基于时间片的轮询调度
4. **系统初始化**: 统一管理所有模块的初始化

## 核心数据结构

### 任务结构体定义
```c
typedef struct {
  void (*task_func)(void);  // 任务函数指针
  uint32_t rate_ms;         // 执行周期（毫秒）
  uint32_t last_run;        // 上次执行时间（初始化为 0，每次运行时刷新）
} scheduler_task_t;
```

**字段说明**:
- `task_func`: 指向任务函数的函数指针，定义了要执行的具体任务
- `rate_ms`: 任务的执行周期，单位为毫秒，决定任务多久执行一次
- `last_run`: 记录任务上次执行的时间戳，用于计算下次执行时间

### 任务数组配置
```c
static scheduler_task_t scheduler_task[] =
{
  {Led_Task, 1, 0},      // LED任务，1ms周期
  {Oled_Task, 10, 0},    // OLED显示任务，10ms周期
  // 其他任务可以根据需要添加或注释
};
```

## 调度算法原理

### 时间片调度机制
调度器采用**非抢占式时间片轮询**算法：

1. **时间基准**: 使用`HAL_GetTick()`获取系统运行时间（毫秒）
2. **周期检查**: 遍历任务数组，检查每个任务是否到达执行时间
3. **任务执行**: 满足条件的任务立即执行
4. **时间更新**: 执行完成后更新任务的`last_run`时间戳

### 调度判断条件
```c
if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
{
    // 执行任务
}
```

**判断逻辑**:
- `now_time`: 当前系统时间
- `scheduler_task[i].rate_ms + scheduler_task[i].last_run`: 任务下次应该执行的时间
- 当当前时间大于等于下次执行时间时，执行该任务

## 核心函数详解

### 1. Scheduler_Init() - 调度器初始化
```c
void Scheduler_Init(void)
{
  System_Init();  // 系统模块初始化
  // 计算任务数组的元素个数
  task_num = sizeof(scheduler_task) / sizeof(scheduler_task_t);
}
```

**功能说明**:
- 调用`System_Init()`初始化所有系统模块
- 自动计算任务数组中的任务数量
- 为调度器运行做准备

### 2. Scheduler_Run() - 调度器运行
```c
void Scheduler_Run(void)
{
  // 遍历任务数组中的所有任务
  for (uint8_t i = 0; i < task_num; i++)
  {
    uint32_t now_time = HAL_GetTick();  // 获取当前时间
    
    // 检查任务是否到达执行时间
    if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
    {
      scheduler_task[i].last_run = now_time;  // 更新执行时间
      scheduler_task[i].task_func();          // 执行任务
    }
  }
}
```

**执行流程**:
1. 遍历所有注册的任务
2. 获取当前系统时间
3. 判断任务是否到达执行时间
4. 执行满足条件的任务
5. 更新任务的执行时间戳

### 3. System_Init() - 系统初始化
```c
void System_Init(void)
{
  Led_Init();      // LED模块初始化
  Key_Init();      // 按键模块初始化
  Oled_Init();     // OLED显示模块初始化
  Uart_Init();     // 串口通信模块初始化
  Gray_Init();     // 灰度传感器模块初始化
  Motor_Init();    // 电机控制模块初始化
  Encoder_Init();  // 编码器模块初始化
  PID_Init();      // PID控制器初始化
  Mpu6050_Init();  // MPU6050传感器初始化
  
  Uart_Printf(&huart1, "=== System Init ===\r\n");
  HAL_TIM_Base_Start_IT(&htim2);  // 启动定时器中断
}
```

## 双重调度机制

本系统采用了**双重调度机制**，结合了主循环调度和中断调度：

### 主循环调度 (Scheduler_Run)
- **执行位置**: 在`main()`函数的无限循环中
- **调度对象**: 低优先级、长周期任务
- **特点**: 非实时，可被中断打断

**当前配置的主循环任务**:
- `Led_Task`: 1ms周期，LED状态管理
- `Oled_Task`: 10ms周期，OLED显示更新

### 中断调度 (TIM2中断)
- **执行位置**: TIM2定时器中断服务程序中
- **调度对象**: 高优先级、实时性要求高的任务
- **特点**: 严格实时，1ms精度

**中断调度的任务**:
```c
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
  if(htim->Instance != htim2.Instance) return;
  
  // 10ms周期任务
  if(++key_timer10ms >= 10) {
    key_timer10ms = 0;
    Key_Task();  // 按键扫描
  }
  
  // 5ms周期任务
  if(++measure_timer5ms >= 5) {
    measure_timer5ms = 0;
    Encoder_Task();  // 编码器数据更新
    Mpu6050_Task();  // MPU6050数据读取
    Gray_Task();     // 灰度传感器数据处理
    PID_Task();      // PID控制计算
  }
  
  // 其他实时任务...
}
```

## 任务周期配置

### 周期分配策略
不同类型的任务根据其实时性要求分配不同的执行周期：

| 任务类型 | 执行周期 | 调度方式 | 实时性要求 |
|---------|---------|----------|-----------|
| LED显示 | 1ms | 主循环 | 低 |
| OLED更新 | 10ms | 主循环 | 低 |
| 按键扫描 | 10ms | 中断 | 中 |
| 传感器读取 | 5ms | 中断 | 高 |
| PID控制 | 5ms | 中断 | 高 |
| 编码器更新 | 5ms | 中断 | 高 |

### 周期选择原则
1. **控制类任务**: 5ms周期，保证控制精度
2. **传感器类任务**: 5ms周期，保证数据实时性
3. **人机交互类任务**: 10ms周期，满足响应需求
4. **显示类任务**: 10ms周期，平衡性能和功耗

## 使用方法

### 1. 添加新任务
要添加新的任务到调度器中，需要以下步骤：

**步骤1**: 在对应的App模块中实现任务函数
```c
void New_Task(void)
{
    // 任务具体实现
}
```

**步骤2**: 在`Scheduler.c`的任务数组中添加任务
```c
static scheduler_task_t scheduler_task[] =
{
  {Led_Task, 1, 0},
  {Oled_Task, 10, 0},
  {New_Task, 20, 0},  // 新任务，20ms周期
};
```

**步骤3**: 在`System_Init()`中添加初始化调用（如果需要）
```c
void System_Init(void)
{
  // 其他初始化...
  New_Init();  // 新模块初始化
}
```

### 2. 调整任务周期
根据实际需求调整任务的执行周期：
```c
{Task_Function, 50, 0},  // 将周期改为50ms
```

### 3. 临时禁用任务
通过注释的方式临时禁用不需要的任务：
```c
// {Unused_Task, 10, 0},  // 临时禁用此任务
```

## 协作式 vs 抢占式调度

### 协作式调度的特点
**优点**:
- **简单易实现**: 不需要复杂的上下文切换
- **资源占用少**: 没有任务栈切换开销
- **调试友好**: 任务执行顺序可预测
- **适合小系统**: 非常适合资源受限的嵌入式系统

**缺点**:
- **实时性有限**: 长任务会影响其他任务的执行
- **需要任务配合**: 任务必须主动让出CPU
- **响应延迟**: 紧急任务可能需要等待当前任务完成

### 与抢占式调度的对比

| 特性 | 协作式调度 | 抢占式调度 |
|------|-----------|-----------|
| 实现复杂度 | 简单 | 复杂 |
| 资源开销 | 低 | 高 |
| 实时性 | 有限 | 强 |
| 任务间影响 | 相互影响 | 相对独立 |
| 调试难度 | 容易 | 困难 |
| 适用场景 | 简单控制系统 | 复杂实时系统 |

## 适用场景

### 适合使用协作式调度的场景
1. **简单控制系统**: 如本工程的小车控制
2. **资源受限系统**: RAM和Flash有限的MCU
3. **实时性要求不严格**: 毫秒级响应即可满足需求
4. **任务相对简单**: 单个任务执行时间较短
5. **学习和原型开发**: 便于理解和快速开发

### 不适合的场景
1. **硬实时系统**: 需要微秒级响应的系统
2. **复杂多任务**: 任务数量多且复杂度高
3. **长时间任务**: 存在执行时间很长的任务
4. **严格优先级**: 需要严格任务优先级管理

## 性能优化建议

### 1. 任务设计原则
- **保持任务简短**: 单个任务执行时间应控制在毫秒级
- **避免阻塞操作**: 不要在任务中使用延时函数
- **合理分配周期**: 根据实际需求设置任务周期
- **减少计算复杂度**: 避免复杂的浮点运算

### 2. 系统优化
- **合理配置时钟**: 确保系统时钟配置正确
- **优化中断优先级**: 合理设置各中断的优先级
- **内存管理**: 避免动态内存分配
- **代码优化**: 使用编译器优化选项

### 3. 调试技巧
- **添加执行时间测量**: 监控任务执行时间
- **使用LED指示**: 通过LED显示系统状态
- **串口调试**: 输出关键信息到串口
- **逻辑分析仪**: 分析任务执行时序

## 常见问题与解决方案

### 1. 任务执行不及时
**原因**: 某个任务执行时间过长
**解决方案**: 
- 分析任务执行时间
- 将长任务拆分为多个短任务
- 优化算法减少计算量

### 2. 系统响应延迟
**原因**: 任务周期设置不合理
**解决方案**:
- 缩短关键任务的执行周期
- 将实时性要求高的任务移到中断中执行

### 3. CPU占用率过高
**原因**: 任务过多或周期过短
**解决方案**:
- 适当延长非关键任务的周期
- 优化任务算法
- 移除不必要的任务

## 扩展功能

### 1. 任务优先级
可以通过修改调度算法实现简单的优先级机制：
```c
// 按优先级排序任务数组，高优先级任务放在前面
static scheduler_task_t scheduler_task[] =
{
  {High_Priority_Task, 5, 0},    // 高优先级
  {Medium_Priority_Task, 10, 0}, // 中优先级
  {Low_Priority_Task, 20, 0},    // 低优先级
};
```

### 2. 任务状态管理
添加任务使能/禁用功能：
```c
typedef struct {
  void (*task_func)(void);
  uint32_t rate_ms;
  uint32_t last_run;
  bool enabled;  // 任务使能标志
} scheduler_task_t;
```

### 3. 任务执行统计
添加任务执行次数和时间统计：
```c
typedef struct {
  void (*task_func)(void);
  uint32_t rate_ms;
  uint32_t last_run;
  uint32_t exec_count;    // 执行次数
  uint32_t exec_time_us;  // 执行时间（微秒）
} scheduler_task_t;
```

---

*本文档详细介绍了STM32F4工程中Scheduler调度系统的设计原理和使用方法，为嵌入式开发初学者提供了完整的学习参考。*

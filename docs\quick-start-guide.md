# STM32F4 智能小车快速入门指南

## 概述

本指南帮助初学者快速上手STM32F4智能小车项目，从硬件准备到软件运行，提供完整的步骤说明和注意事项。

## 硬件准备

### 必需硬件清单
- **主控板**: STM32F407VGT6开发板
- **电机驱动**: TB6612电机驱动板
- **电机**: 带编码器的直流减速电机 × 2
- **传感器**: 
  - MPU6050六轴姿态传感器
  - 8路灰度传感器阵列
- **显示**: 0.96寸OLED显示屏 (SSD1306)
- **其他**: 
  - 按键开关
  - LED指示灯
  - 杜邦线若干
  - 面包板或PCB板

### 硬件连接图

```
STM32F407VGT6 引脚连接:

电机驱动 (TB6612):
├── PWM_A  → TIM1_CH1  (PE9)
├── PWM_B  → TIM1_CH4  (PE14)
├── AIN1   → PE2
├── AIN2   → PE3
├── BIN1   → PE4
└── BIN2   → PE5

编码器:
├── 左编码器 → TIM3_CH1/CH2 (PA6/PA7)
└── 右编码器 → TIM4_CH1/CH2 (PB6/PB7)

I2C设备:
├── OLED (I2C1)    → SCL:PB8, SDA:PB9
└── MPU6050 (I2C3) → SCL:PA8, SDA:PC9

其他:
├── 灰度传感器 → PA0-PA7 (ADC)
├── 按键       → PC13
├── LED        → PC0
└── 串口       → USART1 (PA9/PA10)
```

## 软件环境搭建

### 1. 开发工具安装
```bash
# 必需软件:
1. STM32CubeIDE (推荐) 或 Keil MDK
2. STM32CubeMX (图形化配置工具)
3. ST-Link驱动程序
4. 串口调试工具 (如PuTTY, 串口助手)

# 可选软件:
1. Git (版本控制)
2. Visual Studio Code (代码编辑)
3. STM32 ST-LINK Utility (固件烧录)
```

### 2. 项目导入
```bash
# 方法1: 直接打开项目
1. 启动STM32CubeIDE
2. File → Open Projects from File System
3. 选择项目根目录
4. 点击Finish

# 方法2: 从Git克隆
git clone [项目地址]
cd STM32F4_SmartCar
# 然后按方法1导入
```

### 3. 编译配置
```c
// 在MyDefine.h中配置调试选项
#define DEBUG_ENABLE 1          // 启用调试输出
#define SYSTEM_CLOCK_168MHZ 1   // 系统时钟168MHz
#define USE_FREERTOS 0          // 不使用FreeRTOS

// 编译配置检查
#if DEBUG_ENABLE
    #pragma message("Debug mode enabled")
#endif
```

## 快速启动步骤

### 步骤1: 硬件连接验证
```c
// 运行硬件连接测试
void Hardware_Connection_Test(void)
{
    printf("=== Hardware Connection Test ===\n");
    
    // 1. LED测试
    printf("Testing LED...\n");
    for(int i = 0; i < 5; i++) {
        Led_Display(1);
        HAL_Delay(200);
        Led_Display(0);
        HAL_Delay(200);
    }
    printf("LED: OK\n");
    
    // 2. 按键测试
    printf("Press button to continue...\n");
    while(!Key_Get_State()) {
        HAL_Delay(10);
    }
    printf("Button: OK\n");
    
    // 3. 电机测试
    printf("Testing motors...\n");
    Motor_Set_Speed(&left_motor, 300);
    Motor_Set_Speed(&right_motor, 300);
    HAL_Delay(1000);
    Motor_Set_Speed(&left_motor, 0);
    Motor_Set_Speed(&right_motor, 0);
    printf("Motors: OK\n");
    
    // 4. 传感器测试
    printf("Testing sensors...\n");
    if(HAL_I2C_IsDeviceReady(&hi2c1, 0x78, 3, 1000) == HAL_OK) {
        printf("OLED: OK\n");
    } else {
        printf("OLED: FAILED\n");
    }
    
    if(HAL_I2C_IsDeviceReady(&hi2c3, 0xD0, 3, 1000) == HAL_OK) {
        printf("MPU6050: OK\n");
    } else {
        printf("MPU6050: FAILED\n");
    }
    
    printf("Hardware test complete!\n");
}
```

### 步骤2: 基础功能测试
```c
// 基础功能演示
void Basic_Function_Demo(void)
{
    printf("=== Basic Function Demo ===\n");
    
    // 初始化所有模块
    System_Init();
    
    // OLED显示测试
    OLED_Clear();
    OLED_ShowString(0, 0, "STM32F4 Car", 16);
    OLED_ShowString(0, 16, "Basic Demo", 16);
    OLED_ShowString(0, 32, "Ready!", 16);
    
    // 电机控制演示
    printf("Motor control demo...\n");
    OLED_ShowString(0, 48, "Forward", 16);
    Motor_Set_Speed(&left_motor, 400);
    Motor_Set_Speed(&right_motor, 400);
    HAL_Delay(2000);
    
    OLED_ShowString(0, 48, "Stop   ", 16);
    Motor_Set_Speed(&left_motor, 0);
    Motor_Set_Speed(&right_motor, 0);
    HAL_Delay(1000);
    
    OLED_ShowString(0, 48, "Turn   ", 16);
    Motor_Set_Speed(&left_motor, 300);
    Motor_Set_Speed(&right_motor, -300);
    HAL_Delay(1000);
    
    Motor_Set_Speed(&left_motor, 0);
    Motor_Set_Speed(&right_motor, 0);
    
    // 传感器数据显示
    printf("Sensor data demo...\n");
    for(int i = 0; i < 20; i++) {
        Mpu6050_Task();
        Encoder_Task();
        
        OLED_Clear();
        OLED_ShowString(0, 0, "Sensor Data:", 16);
        
        char str[20];
        sprintf(str, "Pitch:%.1f", pitch);
        OLED_ShowString(0, 16, str, 16);
        
        sprintf(str, "Roll:%.1f", roll);
        OLED_ShowString(0, 32, str, 16);
        
        sprintf(str, "Enc:%d,%d", left_encoder.count, right_encoder.count);
        OLED_ShowString(0, 48, str, 16);
        
        HAL_Delay(500);
    }
    
    OLED_Clear();
    OLED_ShowString(0, 0, "Demo Complete!", 16);
    printf("Basic function demo complete!\n");
}
```

### 步骤3: PID控制测试
```c
// PID控制演示
void PID_Control_Demo(void)
{
    printf("=== PID Control Demo ===\n");
    
    // 初始化PID控制
    PID_Init();
    
    // 设置PID参数
    pid_speed_left.kp = 0.8f;
    pid_speed_left.ki = 0.1f;
    pid_speed_left.kd = 0.05f;
    
    pid_speed_right.kp = 0.8f;
    pid_speed_right.ki = 0.1f;
    pid_speed_right.kd = 0.05f;
    
    // 启用PID控制
    PID_Set_Enable(true);
    
    OLED_Clear();
    OLED_ShowString(0, 0, "PID Control", 16);
    OLED_ShowString(0, 16, "Target: 15cm/s", 16);
    
    // 设置目标速度
    PID_Set_Target_Speed(15.0f, 15.0f);
    
    printf("PID control started, target speed: 15 cm/s\n");
    
    // 运行10秒
    uint32_t start_time = HAL_GetTick();
    while(HAL_GetTick() - start_time < 10000) {
        // 执行PID控制
        PID_Task();
        
        // 更新显示
        char str[20];
        sprintf(str, "L:%.1f R:%.1f", left_encoder.speed_cm_s, right_encoder.speed_cm_s);
        OLED_ShowString(0, 32, "              ", 16); // 清除
        OLED_ShowString(0, 32, str, 16);
        
        sprintf(str, "Out:%d,%d", left_motor.speed, right_motor.speed);
        OLED_ShowString(0, 48, "              ", 16); // 清除
        OLED_ShowString(0, 48, str, 16);
        
        // 输出调试信息
        printf("Target: %.1f,%.1f | Actual: %.1f,%.1f | Output: %d,%d\n",
               pid_speed_left.target, pid_speed_right.target,
               left_encoder.speed_cm_s, right_encoder.speed_cm_s,
               left_motor.speed, right_motor.speed);
        
        HAL_Delay(200);
    }
    
    // 停止PID控制
    PID_Set_Enable(false);
    Motor_Set_Speed(&left_motor, 0);
    Motor_Set_Speed(&right_motor, 0);
    
    OLED_Clear();
    OLED_ShowString(0, 0, "PID Demo", 16);
    OLED_ShowString(0, 16, "Complete!", 16);
    
    printf("PID control demo complete!\n");
}
```

## 常见问题快速解决

### 编译问题
```bash
# 问题1: 找不到头文件
解决方案: 检查Include路径设置
Project → Properties → C/C++ Build → Settings → Tool Settings → MCU GCC Compiler → Include paths

# 问题2: 链接错误
解决方案: 检查源文件是否添加到项目
Project Explorer → 右键项目 → Refresh

# 问题3: 内存不足
解决方案: 优化代码或调整链接脚本
```

### 运行问题
```c
// 问题1: 程序不运行
// 检查系统时钟配置
void Check_System_Clock(void)
{
    uint32_t sysclk = HAL_RCC_GetSysClockFreq();
    printf("System Clock: %lu Hz\n", sysclk);
    
    if(sysclk != 168000000) {
        printf("WARNING: Clock configuration error!\n");
        printf("Expected: 168000000 Hz, Actual: %lu Hz\n", sysclk);
    }
}

// 问题2: 串口无输出
// 检查printf重定向
int _write(int file, char *ptr, int len)
{
    HAL_UART_Transmit(&huart1, (uint8_t*)ptr, len, 1000);
    return len;
}

// 问题3: 电机不转
// 检查PWM输出
void Check_PWM_Output(void)
{
    printf("Checking PWM output...\n");
    
    // 启动PWM
    HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_1);
    HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_4);
    
    // 设置占空比
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, 500);
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_4, 500);
    
    printf("PWM started with 50%% duty cycle\n");
}
```

## 进阶功能开发

### 自定义控制模式
```c
// 添加新的控制模式
typedef enum {
    CONTROL_MODE_MANUAL = 0,    // 手动控制
    CONTROL_MODE_SPEED,         // 速度控制
    CONTROL_MODE_LINE_FOLLOW,   // 循迹模式
    CONTROL_MODE_OBSTACLE,      // 避障模式
    CONTROL_MODE_CUSTOM         // 自定义模式
} Control_Mode_E;

void Set_Control_Mode(Control_Mode_E mode)
{
    switch(mode) {
        case CONTROL_MODE_MANUAL:
            PID_Set_Enable(false);
            printf("Manual control mode\n");
            break;
            
        case CONTROL_MODE_SPEED:
            PID_Set_Enable(true);
            pid_mode = 0; // 速度模式
            printf("Speed control mode\n");
            break;
            
        case CONTROL_MODE_LINE_FOLLOW:
            PID_Set_Enable(true);
            pid_mode = 1; // 循迹模式
            printf("Line following mode\n");
            break;
            
        case CONTROL_MODE_OBSTACLE:
            PID_Set_Enable(true);
            // 启用避障算法
            printf("Obstacle avoidance mode\n");
            break;
            
        case CONTROL_MODE_CUSTOM:
            // 用户自定义控制逻辑
            printf("Custom control mode\n");
            break;
    }
}
```

### 参数调试界面
```c
// 简单的参数调试界面
void Parameter_Debug_Interface(void)
{
    printf("=== Parameter Debug Interface ===\n");
    printf("Commands:\n");
    printf("  kp <value> - Set PID Kp\n");
    printf("  ki <value> - Set PID Ki\n");
    printf("  kd <value> - Set PID Kd\n");
    printf("  speed <left> <right> - Set target speed\n");
    printf("  mode <0|1> - Set PID mode (0:speed, 1:line)\n");
    printf("  status - Show current status\n");
    printf("  help - Show this help\n");
    
    char command[50];
    while(1) {
        printf("> ");
        
        // 简化的命令解析 (实际项目中需要更完善的实现)
        if(scanf("%s", command) == 1) {
            if(strcmp(command, "status") == 0) {
                printf("PID Parameters: Kp=%.3f, Ki=%.3f, Kd=%.3f\n",
                       pid_speed_left.kp, pid_speed_left.ki, pid_speed_left.kd);
                printf("Target Speed: L=%.1f, R=%.1f\n",
                       pid_speed_left.target, pid_speed_right.target);
                printf("Actual Speed: L=%.1f, R=%.1f\n",
                       left_encoder.speed_cm_s, right_encoder.speed_cm_s);
                printf("PID Mode: %s\n", pid_mode ? "Line Following" : "Speed Control");
            }
            else if(strcmp(command, "help") == 0) {
                printf("Available commands listed above.\n");
            }
            else if(strcmp(command, "exit") == 0) {
                break;
            }
            // 其他命令处理...
        }
    }
}
```

## 总结

通过本快速入门指南，您应该能够：

1. **完成硬件连接** - 正确连接所有硬件组件
2. **搭建开发环境** - 安装必要的开发工具
3. **编译运行程序** - 成功编译并下载程序到MCU
4. **验证基础功能** - 确认各个模块工作正常
5. **理解系统架构** - 掌握三层架构设计思想
6. **进行简单调试** - 使用调试工具定位问题

### 下一步学习建议

1. **深入学习PID控制理论** - 理解PID参数调优方法
2. **研究传感器融合算法** - 学习多传感器数据处理
3. **优化系统性能** - 提高实时性和稳定性
4. **扩展功能模块** - 添加新的传感器和执行器
5. **学习高级控制算法** - 如模糊控制、神经网络控制等

---

*本快速入门指南为STM32F4智能小车项目提供了完整的入门路径。建议初学者按照步骤逐一实践，遇到问题时参考FAQ文档或寻求技术支持。*

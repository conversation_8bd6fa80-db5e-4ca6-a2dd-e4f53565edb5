/**
 * @file xxx_driver.h
 * @brief XXX模块驱动层头文件
 * <AUTHOR> Name
 * @date 2024-XX-XX
 * @version 1.0
 */

#ifndef __XXX_DRIVER_H__
#define __XXX_DRIVER_H__

#include "MyDefine.h"

/* ========== 宏定义 ========== */
#define XXX_MAX_VALUE       1000    // 最大值定义
#define XXX_MIN_VALUE       0       // 最小值定义
#define XXX_DEFAULT_TIMEOUT 1000    // 默认超时时间(ms)

/* ========== 类型定义 ========== */

/**
 * @brief XXX模块配置结构体
 */
typedef struct {
    GPIO_TypeDef *gpio_port;        // GPIO端口
    uint16_t gpio_pin;              // GPIO引脚
    TIM_HandleTypeDef *htim;        // 定时器句柄 (如果需要)
    uint32_t channel;               // 通道号 (如果需要)
    uint8_t reverse;                // 反转标志 (0-正常, 1-反转)
} XXX_Config;

/**
 * @brief XXX模块状态枚举
 */
typedef enum {
    XXX_STATE_IDLE = 0,             // 空闲状态
    XXX_STATE_RUNNING,              // 运行状态
    XXX_STATE_ERROR,                // 错误状态
    XXX_STATE_CALIBRATING           // 校准状态
} XXX_State_E;

/**
 * @brief XXX模块实例结构体
 */
typedef struct {
    XXX_Config config;              // 配置信息
    XXX_State_E state;              // 当前状态
    uint32_t last_update_time;      // 上次更新时间
    uint32_t error_count;           // 错误计数
    
    // 数据成员
    uint32_t raw_data;              // 原始数据
    float processed_data;           // 处理后数据
    bool data_ready;                // 数据就绪标志
} XXX;

/* ========== 函数声明 ========== */

/**
 * @brief XXX模块初始化
 * @param instance XXX实例指针
 * @param config 配置参数指针
 * @retval true 初始化成功
 * @retval false 初始化失败
 */
bool XXX_Init(XXX* instance, XXX_Config* config);

/**
 * @brief 启用XXX模块
 * @param instance XXX实例指针
 * @retval None
 */
void XXX_Enable(XXX* instance);

/**
 * @brief 禁用XXX模块
 * @param instance XXX实例指针
 * @retval None
 */
void XXX_Disable(XXX* instance);

/**
 * @brief 复位XXX模块
 * @param instance XXX实例指针
 * @retval None
 */
void XXX_Reset(XXX* instance);

/**
 * @brief 读取原始数据
 * @param instance XXX实例指针
 * @retval 原始数据值
 */
uint32_t XXX_Read_Raw_Data(XXX* instance);

/**
 * @brief 读取处理后数据
 * @param instance XXX实例指针
 * @retval 处理后数据值
 */
float XXX_Read_Processed_Data(XXX* instance);

/**
 * @brief 写入数据
 * @param instance XXX实例指针
 * @param data 要写入的数据
 * @retval true 写入成功
 * @retval false 写入失败
 */
bool XXX_Write_Data(XXX* instance, uint32_t data);

/**
 * @brief 检查模块是否就绪
 * @param instance XXX实例指针
 * @retval true 模块就绪
 * @retval false 模块未就绪
 */
bool XXX_Is_Ready(XXX* instance);

/**
 * @brief 获取模块状态
 * @param instance XXX实例指针
 * @retval 模块当前状态
 */
XXX_State_E XXX_Get_State(XXX* instance);

/**
 * @brief 获取错误计数
 * @param instance XXX实例指针
 * @retval 错误计数值
 */
uint32_t XXX_Get_Error_Count(XXX* instance);

/**
 * @brief 清除错误计数
 * @param instance XXX实例指针
 * @retval None
 */
void XXX_Clear_Error_Count(XXX* instance);

/* ========== 内联函数 ========== */

/**
 * @brief 检查参数有效性 (内联函数)
 * @param instance XXX实例指针
 * @retval true 参数有效
 * @retval false 参数无效
 */
static inline bool XXX_Is_Valid_Instance(XXX* instance)
{
    return (instance != NULL);
}

/**
 * @brief 检查数据范围 (内联函数)
 * @param data 数据值
 * @retval true 数据在有效范围内
 * @retval false 数据超出范围
 */
static inline bool XXX_Is_Valid_Data(uint32_t data)
{
    return (data >= XXX_MIN_VALUE && data <= XXX_MAX_VALUE);
}

#endif /* __XXX_DRIVER_H__ */

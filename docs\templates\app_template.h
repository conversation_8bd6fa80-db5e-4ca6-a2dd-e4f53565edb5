/**
 * @file xxx_app.h
 * @brief XXX模块应用层头文件
 * <AUTHOR> Name
 * @date 2024-XX-XX
 * @version 1.0
 */

#ifndef __XXX_APP_H__
#define __XXX_APP_H__

#include "MyDefine.h"

/* ========== 宏定义 ========== */
#define XXX_APP_TASK_PERIOD     50      // 任务执行周期(ms)
#define XXX_APP_MAX_INSTANCES   4       // 最大实例数量
#define XXX_APP_BUFFER_SIZE     256     // 缓冲区大小

/* ========== 类型定义 ========== */

/**
 * @brief XXX应用工作模式枚举
 */
typedef enum {
    XXX_MODE_MANUAL = 0,        // 手动模式
    XXX_MODE_AUTO,              // 自动模式
    XXX_MODE_CALIBRATION,       // 校准模式
    XXX_MODE_TEST               // 测试模式
} XXX_Mode_E;

/**
 * @brief XXX应用参数结构体
 */
typedef struct {
    XXX_Mode_E mode;            // 工作模式
    uint32_t update_period;     // 更新周期(ms)
    float threshold_high;       // 高阈值
    float threshold_low;        // 低阈值
    bool enable_filter;         // 使能滤波
    float filter_coefficient;   // 滤波系数
    bool enable_auto_calibration; // 使能自动校准
} XXX_Params_t;

/**
 * @brief XXX应用状态结构体
 */
typedef struct {
    bool initialized;           // 初始化标志
    bool running;              // 运行标志
    uint32_t run_count;        // 运行计数
    uint32_t error_count;      // 错误计数
    uint32_t last_update_time; // 上次更新时间
    float current_value;       // 当前值
    float filtered_value;      // 滤波后的值
    bool threshold_exceeded;   // 阈值超限标志
} XXX_Status_t;

/**
 * @brief XXX应用统计信息结构体
 */
typedef struct {
    uint32_t total_samples;     // 总采样数
    float min_value;           // 最小值
    float max_value;           // 最大值
    float average_value;       // 平均值
    uint32_t threshold_violations; // 阈值违规次数
} XXX_Statistics_t;

/**
 * @brief XXX应用回调函数类型
 */
typedef void (*XXX_Callback_t)(float value, void* user_data);

/* ========== 全局变量声明 ========== */
extern XXX_Params_t g_xxx_params;      // 全局参数
extern XXX_Status_t g_xxx_status;      // 全局状态

/* ========== 函数声明 ========== */

/**
 * @brief XXX应用初始化
 * @param None
 * @retval true 初始化成功
 * @retval false 初始化失败
 */
bool XXX_App_Init(void);

/**
 * @brief XXX应用去初始化
 * @param None
 * @retval None
 */
void XXX_App_Deinit(void);

/**
 * @brief XXX主任务函数
 * @param None
 * @retval None
 * @note 此函数应该被调度器周期性调用
 */
void XXX_Task(void);

/**
 * @brief 启动XXX应用
 * @param None
 * @retval true 启动成功
 * @retval false 启动失败
 */
bool XXX_Start(void);

/**
 * @brief 停止XXX应用
 * @param None
 * @retval None
 */
void XXX_Stop(void);

/**
 * @brief 重置XXX应用
 * @param None
 * @retval None
 */
void XXX_Reset(void);

/**
 * @brief 设置XXX应用参数
 * @param params 参数结构体指针
 * @retval true 设置成功
 * @retval false 设置失败
 */
bool XXX_Set_Params(const XXX_Params_t* params);

/**
 * @brief 获取XXX应用参数
 * @param params 参数结构体指针
 * @retval true 获取成功
 * @retval false 获取失败
 */
bool XXX_Get_Params(XXX_Params_t* params);

/**
 * @brief 获取XXX应用状态
 * @param status 状态结构体指针
 * @retval true 获取成功
 * @retval false 获取失败
 */
bool XXX_Get_Status(XXX_Status_t* status);

/**
 * @brief 获取XXX应用统计信息
 * @param stats 统计信息结构体指针
 * @retval true 获取成功
 * @retval false 获取失败
 */
bool XXX_Get_Statistics(XXX_Statistics_t* stats);

/**
 * @brief 清除XXX应用统计信息
 * @param None
 * @retval None
 */
void XXX_Clear_Statistics(void);

/**
 * @brief 设置工作模式
 * @param mode 工作模式
 * @retval true 设置成功
 * @retval false 设置失败
 */
bool XXX_Set_Mode(XXX_Mode_E mode);

/**
 * @brief 获取工作模式
 * @param None
 * @retval 当前工作模式
 */
XXX_Mode_E XXX_Get_Mode(void);

/**
 * @brief 设置阈值
 * @param high_threshold 高阈值
 * @param low_threshold 低阈值
 * @retval true 设置成功
 * @retval false 设置失败
 */
bool XXX_Set_Threshold(float high_threshold, float low_threshold);

/**
 * @brief 获取当前值
 * @param None
 * @retval 当前值
 */
float XXX_Get_Current_Value(void);

/**
 * @brief 获取滤波后的值
 * @param None
 * @retval 滤波后的值
 */
float XXX_Get_Filtered_Value(void);

/**
 * @brief 注册阈值超限回调函数
 * @param callback 回调函数指针
 * @param user_data 用户数据指针
 * @retval true 注册成功
 * @retval false 注册失败
 */
bool XXX_Register_Threshold_Callback(XXX_Callback_t callback, void* user_data);

/**
 * @brief 注销阈值超限回调函数
 * @param None
 * @retval None
 */
void XXX_Unregister_Threshold_Callback(void);

/**
 * @brief 执行校准
 * @param None
 * @retval true 校准成功
 * @retval false 校准失败
 */
bool XXX_Calibrate(void);

/**
 * @brief 执行自检
 * @param None
 * @retval true 自检通过
 * @retval false 自检失败
 */
bool XXX_Self_Test(void);

/**
 * @brief 保存配置到Flash
 * @param None
 * @retval true 保存成功
 * @retval false 保存失败
 */
bool XXX_Save_Config(void);

/**
 * @brief 从Flash加载配置
 * @param None
 * @retval true 加载成功
 * @retval false 加载失败
 */
bool XXX_Load_Config(void);

/* ========== 内联函数 ========== */

/**
 * @brief 检查应用是否已初始化 (内联函数)
 * @param None
 * @retval true 已初始化
 * @retval false 未初始化
 */
static inline bool XXX_Is_Initialized(void)
{
    return g_xxx_status.initialized;
}

/**
 * @brief 检查应用是否正在运行 (内联函数)
 * @param None
 * @retval true 正在运行
 * @retval false 未运行
 */
static inline bool XXX_Is_Running(void)
{
    return g_xxx_status.running;
}

/**
 * @brief 检查阈值是否超限 (内联函数)
 * @param None
 * @retval true 阈值超限
 * @retval false 阈值正常
 */
static inline bool XXX_Is_Threshold_Exceeded(void)
{
    return g_xxx_status.threshold_exceeded;
}

#endif /* __XXX_APP_H__ */
